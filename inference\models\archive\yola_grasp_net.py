import torch
import torch.nn as nn
import torch.nn.functional as F
from .grasp_model import GraspModel

class DoubleConv(nn.Module):
    """(convolution => [BN] => ReLU) * 2"""
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """Downscaling with maxpool then double conv"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """Upscaling then double conv"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)


# ===============================================================
# 2. 新增的预处理模块 (New Pre-processing Modules)
# ===============================================================

class NoiseDecouplingNetwork(nn.Module):
    """
    噪声解耦网络 (Noise-Decoupling Network)。
    目标：将输入的带噪图像 I_noisy 分解为 结构层 I_structure 和 噪声层 N_noise。
    这是一个轻量级的编码器-解码器结构。
    """
    def __init__(self, input_channels=3):
        super().__init__()
        # 编码器部分
        self.enc1 = DoubleConv(input_channels, 32)
        self.enc2 = Down(32, 64)
        
        # 解码器部分
        self.dec1 = Up(64, 32)
        
        # 输出层
        # 输出结构层 (与输入通道数相同)
        self.out_structure = nn.Conv2d(32, input_channels, kernel_size=1)
        # 输出噪声层 (与输入通道数相同)
        self.out_noise = nn.Conv2d(32, input_channels, kernel_size=1)

    def forward(self, x):
        # 编码
        x1 = self.enc1(x)
        x2 = self.enc2(x1)
        
        # 解码
        x_dec = self.dec1(x2, x1)
        
        # 预测结构层和噪声层
        structure_layer = self.out_structure(x_dec)
        noise_layer = self.out_noise(x_dec)
        
        # 使用 tanh 激活函数将结构层的值限制在 [-1, 1] 范围内 (假设输入图像已归一化到此范围)
        structure_layer = torch.tanh(structure_layer)
        
        return structure_layer, noise_layer

class ReflectionSeparationModule(nn.Module):
    """
    反射分离模块 (Reflection Separation Module)。
    目标：将输入的结构层 I_structure 分解为 漫反射层 I_diffuse 和 镜面反射层 I_specular。
    """
    def __init__(self, input_channels=3):
        super().__init__()
        # 使用几个简单的卷积层来完成这个任务
        self.conv_block = nn.Sequential(
            DoubleConv(input_channels, 32),
            DoubleConv(32, 32)
        )
        # 输出漫反射层
        self.out_diffuse = nn.Conv2d(32, input_channels, kernel_size=1)
        # 输出镜面反射层
        self.out_specular = nn.Conv2d(32, input_channels, kernel_size=1)

    def forward(self, x):
        features = self.conv_block(x)
        diffuse_layer = self.out_diffuse(features)
        specular_layer = self.out_specular(features)
        
        # 使用 tanh 和 relu 作为激活函数
        # 漫反射层应该与原始图像范围一致
        diffuse_layer = torch.tanh(diffuse_layer)
        # 镜面反射层（高光）应该是正值
        specular_layer = F.relu(specular_layer)
        
        return diffuse_layer, specular_layer

# ===============================================================
# 3. 整合后的主模型 (The Main Integrated Model)
# ===============================================================

class YOLAGraspUNet(GraspModel):
    """
    集成了噪声解耦和反射分离的U-Net抓取模型。
    """
    def __init__(self, input_channels=3, output_channels=1, channel_size=32, bilinear=True):
        super().__init__()
        
        # ------------------- 预处理前端 -------------------
        self.noise_decoupler = NoiseDecouplingNetwork(input_channels)
        self.reflection_separator = ReflectionSeparationModule(input_channels)
        
        # ------------------- U-Net 主体 (用于抓取检测) -------------------
        # U-Net的输入通道数现在是预处理模块的输出通道数
        self.unet_input_channels = input_channels # 我们将漫反射层作为U-Net的输入

        self.inc = DoubleConv(self.unet_input_channels, channel_size)
        self.down1 = Down(channel_size, channel_size * 2)
        self.down2 = Down(channel_size * 2, channel_size * 4)
        self.down3 = Down(channel_size * 4, channel_size * 8)
        factor = 2 if bilinear else 1
        self.down4 = Down(channel_size * 8, channel_size * 16 // factor)
        self.up1 = Up(channel_size * 16, channel_size * 8 // factor, bilinear)
        self.up2 = Up(channel_size * 8, channel_size * 4 // factor, bilinear)
        self.up3 = Up(channel_size * 4, channel_size * 2 // factor, bilinear)
        self.up4 = Up(channel_size * 2, channel_size, bilinear)

        # 抓取预测头
        self.pos_output = nn.Conv2d(channel_size, output_channels, kernel_size=1)
        self.cos_output = nn.Conv2d(channel_size, output_channels, kernel_size=1)
        self.sin_output = nn.Conv2d(channel_size, output_channels, kernel_size=1)
        self.width_output = nn.Conv2d(channel_size, output_channels, kernel_size=1)

        # 初始化权重
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.xavier_uniform_(m.weight, gain=1)

    def forward(self, x_in):
        """
        定义模型的前向传播路径。
        路径: 输入 -> 噪声解耦 -> 反射分离 -> U-Net -> 抓取预测
        """
        # --- 阶段1: 噪声解耦 ---
        # 输入 x_in 是原始的、带噪声的低光照图像
        structure_layer, noise_layer = self.noise_decoupler(x_in)
        
        # --- 阶段2: 反射分离 ---
        # 将去噪后的结构层送入反射分离模块
        diffuse_layer, specular_layer = self.reflection_separator(structure_layer)
        
        # --- 阶段3: U-Net 主干网络进行抓取特征提取 ---
        # 我们选择将“漫反射层”作为U-Net的输入，因为它理论上最干净，最适合分析物体自身属性
        x1 = self.inc(diffuse_layer)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x_feat = self.up4(x, x1) # 最终的特征图
        
        # --- 阶段4: 抓取预测 ---
        pos_output = self.pos_output(x_feat)
        cos_output = self.cos_output(x_feat)
        sin_output = self.sin_output(x_feat)
        width_output = self.width_output(x_feat)
        
        # ------------------- 计算辅助损失 (Auxiliary Losses) -------------------
        # 这些损失用于监督预处理模块的学习，非常关键
        
        # 1. 噪声重构损失: 确保 结构层 + 噪声层 ≈ 原始输入
        # 这是自监督的关键，强迫网络学习有意义的分解
        noise_recon_loss = F.l1_loss(structure_layer + noise_layer, x_in)
        
        # 2. 反射重构损失: 确保 漫反射层 + 镜面反射层 ≈ 结构层
        reflection_recon_loss = F.l1_loss(diffuse_layer + specular_layer, structure_layer)

        # 3. 镜面反射稀疏损失: 鼓励镜面反射层是稀疏的（大部分区域为0）
        # 因为高光通常只占图像的一小部分
        specular_sparsity_loss = torch.mean(torch.abs(specular_layer))

        return {
            'grasp_predictions': {
                'pos': pos_output,
                'cos': cos_output,
                'sin': sin_output,
                'width': width_output
            },
            'decompositions': {
                'structure': structure_layer,
                'noise': noise_layer,
                'diffuse': diffuse_layer,
                'specular': specular_layer
            },
            'aux_losses': {
                'noise_recon': noise_recon_loss,
                'reflection_recon': reflection_recon_loss,
                'specular_sparsity': specular_sparsity_loss
            }
        }

# ===============================================================
# 4. 使用示例 (Example Usage)
# ===============================================================

if __name__ == '__main__':
    # 创建一个模型实例
    # 假设输入是 3 通道的 RGB 图像 (224x224)
    model = YOLAGraspUNet(input_channels=3, output_channels=1)
    
    # 创建一个随机的输入张量来模拟一批数据
    # batch_size=2, channels=3, height=224, width=224
    dummy_input = torch.randn(2, 3, 224, 224)
    
    print("输入张量尺寸:", dummy_input.shape)
    print("-" * 30)
    
    # 执行前向传播
    output_dict = model(dummy_input)
    
    # 打印输出的各个部分
    print("输出字典键:", output_dict.keys())
    print("-" * 30)
    
    print("抓取预测结果:")
    for name, tensor in output_dict['grasp_predictions'].items():
        print(f"  - {name}: {tensor.shape}")
    print("-" * 30)
        
    print("图像分解结果:")
    for name, tensor in output_dict['decompositions'].items():
        print(f"  - {name}: {tensor.shape}")
    print("-" * 30)

    print("辅助损失项:")
    for name, loss in output_dict['aux_losses'].items():
        print(f"  - {name}: {loss.item():.4f}")
    print("-" * 30)
        
    # --- 模拟损失计算 ---
    # 创建虚拟的真实标签
    gt_pos = torch.rand(2, 1, 224, 224)
    gt_cos = torch.rand(2, 1, 224, 224)
    gt_sin = torch.rand(2, 1, 224, 224)
    gt_width = torch.rand(2, 1, 224, 224)
    dummy_gt = (gt_pos, gt_cos, gt_sin, gt_width)
    
    # 计算总损失
    loss_dict = model.compute_loss(dummy_input, dummy_gt)
    
    # 将抓取损失和所有辅助损失相加得到最终的总损失
    total_loss = loss_dict['grasp_loss']
    for loss_name, loss_value in loss_dict['aux_losses'].items():
        # 这里可以为不同的辅助损失设置不同的权重
        if loss_name == 'noise_recon':
            total_loss += 0.5 * loss_value 
        elif loss_name == 'reflection_recon':
            total_loss += 0.5 * loss_value
        elif loss_name == 'specular_sparsity':
            total_loss += 0.1 * loss_value

    print(f"抓取损失 (Grasp Loss): {loss_dict['grasp_loss'].item():.4f}")
    print(f"最终总损失 (Total Loss): {total_loss.item():.4f}")
    
    # 在实际训练中，你会对 total_loss 进行反向传播
    # total_loss.backward()