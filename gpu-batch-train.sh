#!/bin/bash
# gpu-simple - 真正简单的GPU训练脚本
# 原则：直接、可靠、零复杂性

set -euo pipefail

# ===================
# 实验配置 - 统一管理
# ===================
declare -a EXPERIMENTS=(
    "cornell:/home/<USER>/datasets/cornell-with-low-light:yola_grasp_net_v4:training_very_dark_cornell_with_yola_grasp_net_v4"
    "cornell:/home/<USER>/datasets/cornell-with-low-light:grconvnet3:training_very_dark_cornell_with_grconvnet3"
    "cornell:/home/<USER>/datasets/cornell-with-low-light:senet:training_very_dark_cornell_with_senet"
    "cornell:/home/<USER>/datasets/cornell-with-low-light:grasp-transformer:training_very_dark_cornell_with_grasp-transformer"
    "cornell:/home/<USER>/datasets/cornell-with-low-light:dsnet:training_very_dark_cornell_with_dsnet"
    "cornell:/home/<USER>/datasets/cornell-with-low-light:skgnet:training_very_dark_cornell_with_skgnet"
)

# ===================
# 配置参数
# ===================
LOG_DIR="logs/$(date +%Y%m%d_%H%M%S)_simple_training"

# ===================
# 工具函数
# ===================

# 解析实验字符串
parse_experiment() {
    local exp_string=$1
    IFS=':' read -r dataset dataset_path network description <<< "$exp_string"
    echo "$dataset" "$dataset_path" "$network" "$description"
}

# 启动单个训练任务
start_training() {
    local dataset=$1
    local dataset_path=$2
    local network=$3
    local description=$4
    local gpu_id=$5
    
    local log_file="$LOG_DIR/${description}_gpu${gpu_id}.log"
    
    echo "启动: $network -> GPU $gpu_id"
    
    # 使用 CUDA_VISIBLE_DEVICES 环境变量来指定GPU
    # nohup 将在后台独立运行，即使关闭终端
    nohup python train_network2.py \
        --dataset "$dataset" \
        --dataset-path "$dataset_path" \
        --network "$network" \
        --description "$description" \
        > "$log_file" 2>&1 &
    
    echo "  PID: $! | 日志: $log_file"
}

# 清理函数
cleanup() {
    echo "收到中断信号，清理训练任务..."
    # 使用 pkill 更可靠地杀死所有相关训练进程
    pkill -f train_network2.py 2>/dev/null || true
    echo "清理完成"
    exit 0
}

# 捕获 Ctrl+C (SIGINT) 和 kill (SIGTERM) 信号
trap cleanup SIGINT SIGTERM

# ===================
# 主逻辑
# ===================

main() {
    # 检查依赖
    command -v python >/dev/null || { echo "错误: 需要python"; exit 1; }
    [[ ! -f train_network2.py ]] && { echo "错误: 找不到 train_network2.py"; exit 1; }
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    echo "开始GPU训练任务"
    echo "日志目录: $LOG_DIR"
    echo "实验总数: ${#EXPERIMENTS[@]}"
    echo "================================"
    
    # --- 在 GPU 0 上运行前三个实验 ---
    echo "=== 分配任务到 GPU 0 ==="
    export CUDA_VISIBLE_DEVICES=0
    for i in {0..2}; do
        if [[ $i -ge ${#EXPERIMENTS[@]} ]]; then
            echo "警告: 实验索引 $i 超出范围，跳过。"
            continue
        fi
        
        local exp="${EXPERIMENTS[$i]}"
        read -r dataset dataset_path network description <<< "$(parse_experiment "$exp")"
        start_training "$dataset" "$dataset_path" "$network" "$description" 0
        sleep 1 # 短暂延迟，避免同时启动可能引发的资源竞争
    done
    echo ""

    # --- 在 GPU 1 上运行后三个实验 ---
    echo "=== 分配任务到 GPU 1 ==="
    export CUDA_VISIBLE_DEVICES=1
    for i in {3..5}; do
        if [[ $i -ge ${#EXPERIMENTS[@]} ]]; then
            echo "警告: 实验索引 $i 超出范围，跳过。"
            continue
        fi
        
        local exp="${EXPERIMENTS[$i]}"
        read -r dataset dataset_path network description <<< "$(parse_experiment "$exp")"
        start_training "$dataset" "$dataset_path" "$network" "$description" 1
        sleep 1
    done
    echo ""
    
    echo "所有实验已启动！"
    echo ""
    echo "监控命令:"
    echo "  查看进程: ps aux | grep train_network2"
    echo "  查看日志: tail -f $LOG_DIR/*.log"
    echo "  实时监控GPU: watch nvidia-smi"
    echo ""
    echo "停止所有训练: pkill -f train_network2.py"
}

main "$@"