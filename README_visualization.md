# 图像预处理可视化脚本 - 使用说明（带抓取框可视化）

## 概述
这个脚本可以可视化Cornell抓取数据集的图像预处理流程，完全按照实际训练过程进行处理，并且可以显示抓取框在每个处理步骤中的变化。

## 主要特性
- ✅ 使用真实的抓取标注数据来确定裁剪位置
- ✅ 支持自定义旋转角度（模拟训练中的数据增强）
- ✅ 支持自定义缩放因子（模拟训练中的数据增强）
- ✅ 完全匹配训练流程的处理顺序和参数
- 🆕 **抓取框可视化**：显示抓取框在处理过程中的变化
- 🆕 **抓取输出可视化**：显示最终的抓取质量、角度和宽度图

## 使用方法

### 基本用法（显示预处理步骤+抓取框）
```bash
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff"
```

### 显示最终抓取输出（位置、角度、宽度图）
```bash
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff" \
    --show_outputs
```

### 模拟训练中的数据增强
```bash
# 90度旋转 + 抓取框变换
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff" \
    --rotation 1.57 \
    --show_outputs

# 180度旋转 + 0.7倍缩放 + 完整可视化
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff" \
    --rotation 3.14 \
    --zoom 0.7 \
    --show_outputs
```

## 参数说明

### 必需参数
- `--rgb_path`: RGB图像路径
- `--depth_path`: 深度图像路径

### 可选参数
- `--output_size`: 输出图像尺寸（默认：224）
- `--rotation`: 旋转角度（弧度，默认：0.0）
- `--zoom`: 缩放因子（0.5-1.0，默认：1.0）
- `--use_real_params`: 使用基于抓取标注的真实参数（默认：True）
- `--use_fixed_params`: 使用固定参数而非抓取标注参数
- 🆕 `--show_grasps`: 显示抓取框覆盖（默认：True）
- 🆕 `--show_outputs`: 显示最终抓取输出图（默认：False）

## 可视化内容

### 1. 预处理步骤可视化
显示6个处理步骤，每个步骤都可以看到：
- **图像变化**：原始→旋转→裁剪→[归一化*]→缩放→调整尺寸→[归一化**]
- **抓取框变化**：
  - 🔴 **红色框**：原始抓取标注
  - 🟢 **绿色框**：经过所有变换后的最终抓取框

### 2. 抓取输出可视化（使用 `--show_outputs`）
显示4个子图：
1. **最终图像+抓取框**：处理完成的图像和变换后的抓取框
2. **抓取质量图**：显示每个像素的抓取质量分数
3. **抓取角度图**：显示每个像素的抓取角度
4. **抓取宽度图**：显示每个像素的抓取宽度（归一化）

## 处理流程

### 图像处理步骤
1. **原始图像** - 加载的原始RGB/深度图像 + 原始抓取框
2. **旋转后** - 绕抓取中心点旋转
3. **裁剪后** - 基于抓取中心裁剪为指定尺寸
4. **归一化** (深度图) - 深度图在缩放前归一化
5. **缩放后** - 应用缩放因子
6. **调整尺寸后** - 调整为最终输出尺寸 + 最终抓取框

### 抓取框处理步骤
抓取框经过与图像完全相同的变换：
1. **旋转**：绕相同中心点旋转
2. **平移**：根据裁剪偏移进行平移
3. **缩放**：应用相同的缩放因子

## 示例输出

### 预处理可视化
- **2x3 网格布局**：显示6个处理步骤
- **红色抓取框**：只在原始图像中显示
- **绿色抓取框**：在最终处理图像中显示
- **图例**：区分原始和最终抓取框

### 抓取输出可视化
- **2x2 网格布局**：
  - 左上：最终图像+抓取框
  - 右上：抓取质量热图
  - 左下：抓取角度图（HSV色彩编码）
  - 右下：抓取宽度图（归一化）

## 使用场景

1. **调试预处理管道**：查看每个步骤对图像和抓取框的影响
2. **验证数据增强**：确认旋转和缩放对抓取标注的正确变换
3. **理解网络输入**：查看最终输入网络的图像格式和抓取标注
4. **分析抓取质量**：通过热图理解抓取质量分布
5. **研究抓取角度**：可视化抓取方向的分布

## 注意事项

1. 只有使用真实参数时才会显示抓取框
2. 抓取输出可视化需要有效的抓取标注文件
3. 绿色抓取框代表网络训练时实际使用的标注
4. 抓取质量图的数值范围是[0,1]，颜色越亮表示抓取质量越高
