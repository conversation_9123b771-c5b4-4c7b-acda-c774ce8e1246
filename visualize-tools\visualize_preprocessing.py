#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Visualization script for image preprocessing pipeline in robotic grasping system.
This script visualizes the steps RGB/Depth images go through before entering the network.


# 使用真实训练参数（默认）
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff"

# 模拟90度旋转的数据增强
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff" \
    --rotation 1.57

# 模拟随机缩放的数据增强
python visualize_preprocessing.py \
    --rgb_path "E:/Dataset/cornell/01/pcd0105r.png" \
    --depth_path "E:/Dataset/cornell/01/pcd0105d.tiff" \
    --zoom 0.7
"""

import argparse
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from imageio import imread
from skimage.transform import rotate, resize

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.dataset_processing.image import Image, DepthImage
from utils.dataset_processing import grasp


def get_corresponding_grasp_file(image_path):
    """
    Get the corresponding grasp annotation file for an image.
    
    Args:
        image_path (str): Path to RGB or depth image
    
    Returns:
        str: Path to the corresponding grasp annotation file
    """
    # Convert image path to grasp file path
    if image_path.endswith('r.png'):
        grasp_file = image_path.replace('r.png', 'cpos.txt')
    elif image_path.endswith('d.tiff'):
        grasp_file = image_path.replace('d.tiff', 'cpos.txt')
    else:
        # Try to infer from the path structure
        dir_path = os.path.dirname(image_path)
        filename = os.path.basename(image_path)
        # Extract the base name (e.g., pcd0105 from pcd0105r.png)
        base_name = filename.split('.')[0]
        if base_name.endswith('r') or base_name.endswith('d'):
            base_name = base_name[:-1]
        grasp_file = os.path.join(dir_path, base_name + 'cpos.txt')
    
    return grasp_file


def get_crop_attrs(grasp_file, output_size=224):
    """
    Get crop attributes based on grasp annotations, following the same logic as CornellDataset.
    
    Args:
        grasp_file (str): Path to grasp annotation file
        output_size (int): Output size for the image
    
    Returns:
        tuple: (center, left, top) where center is the grasp center and left, top are crop positions
    """
    gtbbs = grasp.GraspRectangles.load_from_cornell_file(grasp_file)
    center = gtbbs.center
    left = max(0, min(center[1] - output_size // 2, 640 - output_size))
    top = max(0, min(center[0] - output_size // 2, 480 - output_size))
    return center, left, top


def load_and_preprocess_image(image_path, is_depth=False, output_size=224, rotation=0.0, zoom_factor=1.0, use_real_params=True):
    """
    Load and preprocess an image following the same steps as in the dataset processing.
    
    Args:
        image_path (str): Path to the image file
        is_depth (bool): Whether the image is a depth image
        output_size (int): Output size for the image
        rotation (float): Rotation angle in radians
        zoom_factor (float): Zoom factor (0.5-1.0)
        use_real_params (bool): Whether to use real grasp-based parameters or fixed ones
    
    Returns:
        dict: Dictionary containing images at each preprocessing step and grasp info
    """
    # Step 1: Load image
    if is_depth:
        img = DepthImage.from_tiff(image_path)
    else:
        img = Image.from_file(image_path)
    
    results = {
        'original': img.copy().img,
        'grasp_info': None
    }
    
    # Get crop parameters based on grasp annotations or use fixed values
    if use_real_params:
        try:
            grasp_file = get_corresponding_grasp_file(image_path)
            if os.path.exists(grasp_file):
                center, left, top = get_crop_attrs(grasp_file, output_size)
                bottom = min(480, top + output_size)
                right = min(640, left + output_size)
                print(f"Using grasp-based crop: center={center}, crop=({top},{left})-({bottom},{right})")
                
                # Load and process grasp rectangles for visualization
                gtbbs_original = grasp.GraspRectangles.load_from_cornell_file(grasp_file)
                results['grasp_info'] = {
                    'original_grasps': gtbbs_original.copy(),
                    'center': center,
                    'crop_params': (top, left, bottom, right),
                    'rotation': rotation,
                    'zoom_factor': zoom_factor,
                    'output_size': output_size
                }
            else:
                print(f"Grasp file not found: {grasp_file}, using fixed parameters")
                center = [240, 320]  # Image center for 480x640 Cornell images
                top, left = 100, 100
                bottom, right = min(top + output_size, img.img.shape[0]), min(left + output_size, img.img.shape[1])
        except Exception as e:
            print(f"Error loading grasp file: {e}, using fixed parameters")
            center = [240, 320]
            top, left = 100, 100
            bottom, right = min(top + output_size, img.img.shape[0]), min(left + output_size, img.img.shape[1])
    else:
        # Use fixed crop parameters for simple visualization
        center = [240, 320]
        top, left = 100, 100
        bottom, right = min(top + output_size, img.img.shape[0]), min(left + output_size, img.img.shape[1])
    
    # Step 2: Rotate (following the exact same process as in training)
    img.rotate(rotation, center)
    results['rotated'] = img.copy().img
    
    # Step 3: Crop (following the exact same process as in training)
    img.crop((top, left), (bottom, right))
    results['cropped'] = img.copy().img
    
    # Step 4: Normalize (for depth images, normalize before zoom/resize - matching training)
    if is_depth:
        img.normalise()
        results['normalized_early'] = img.copy().img
    
    # Step 5: Zoom (following the exact same process as in training)
    img.zoom(zoom_factor)
    results['zoomed'] = img.copy().img
    
    # Step 6: Resize (following the exact same process as in training)
    img.resize((output_size, output_size))
    results['resized'] = img.copy().img
    
    # Step 7: Normalize (for RGB images, normalize after resize - matching training)
    if not is_depth:
        img.normalise()
        # For RGB, also transpose to CHW format
        img.img = img.img.transpose((2, 0, 1))
    
    results['normalized'] = img.img
    
    # Process grasp rectangles through the same transformations
    if results['grasp_info'] is not None:
        results['grasp_info']['final_grasps'] = process_grasp_rectangles(results['grasp_info'])
    
    return results


def process_grasp_rectangles(grasp_info):
    """
    Process grasp rectangles through the same transformations as the image.
    
    Args:
        grasp_info (dict): Dictionary containing grasp information
    
    Returns:
        GraspRectangles: Processed grasp rectangles
    """
    gtbbs = grasp_info['original_grasps'].copy()
    center = grasp_info['center']
    top, left, bottom, right = grasp_info['crop_params']
    rotation = grasp_info['rotation']
    zoom_factor = grasp_info['zoom_factor']
    output_size = grasp_info['output_size']
    
    # Apply the same transformations as in CornellDataset.get_gtbb()
    gtbbs.rotate(rotation, center)
    gtbbs.offset((-top, -left))
    gtbbs.zoom(zoom_factor, (output_size // 2, output_size // 2))
    
    return gtbbs


def visualize_preprocessing(results, title_prefix=""):
    """
    Visualize the preprocessing steps with grasp rectangles overlay.
    
    Args:
        results (dict): Dictionary containing images at each preprocessing step
        title_prefix (str): Prefix for plot titles
    """
    # Determine which steps to show based on available results
    if title_prefix == "Depth " and 'normalized_early' in results:
        steps = ['original', 'rotated', 'cropped', 'normalized_early', 'zoomed', 'resized']
        titles = ['Original', 'Rotated', 'Cropped', 'Normalized', 'Zoomed', 'Resized']
    else:
        steps = ['original', 'rotated', 'cropped', 'zoomed', 'resized', 'normalized']
        titles = ['Original', 'Rotated', 'Cropped', 'Zoomed', 'Resized', 'Normalized']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # Check if we have grasp information
    has_grasps = results.get('grasp_info') is not None and results['grasp_info'].get('final_grasps') is not None
    
    for i, (step, title) in enumerate(zip(steps, titles)):
        ax = axes[i]
        img = results[step]
        
        # Handle different image formats
        display_img = img.copy()
        if len(img.shape) == 3:
            # For RGB images, transpose back to HWC for visualization
            if img.shape[0] == 3:  # CHW format
                display_img = img.transpose((1, 2, 0))
            # For other 3D images, show first channel
            elif 'normalized' in step:
                display_img = img[0] if not title_prefix else img
        elif len(img.shape) == 2 and 'normalized' in step and title_prefix == 'Depth ':
            # Depth normalized image
            pass
            
        # Display image
        if 'normalized' in step:
            im = ax.imshow(display_img, cmap='viridis' if title_prefix == 'Depth ' else None)
        else:
            # For original images, ensure proper scaling
            if display_img.max() <= 1.0:
                im = ax.imshow(display_img, cmap='viridis' if title_prefix == 'Depth ' else None)
            else:
                im = ax.imshow(display_img.astype(np.uint8), cmap='viridis' if title_prefix == 'Depth ' else None)
        
        # Overlay grasp rectangles
        if has_grasps:
            if step == 'original':
                # Show original grasp rectangles
                original_grasps = results['grasp_info']['original_grasps']
                for gr in original_grasps:
                    ax.plot(gr.points[:, 1], gr.points[:, 0], 'r-', linewidth=2, alpha=0.8)
                    ax.plot([gr.points[-1, 1], gr.points[0, 1]], 
                           [gr.points[-1, 0], gr.points[0, 0]], 'r-', linewidth=2, alpha=0.8)
            elif step in ['resized'] or (step == 'normalized_early' and title_prefix == "Depth "):
                # Show final processed grasp rectangles
                final_grasps = results['grasp_info']['final_grasps']
                for gr in final_grasps:
                    ax.plot(gr.points[:, 1], gr.points[:, 0], 'lime', linewidth=2, alpha=0.9)
                    ax.plot([gr.points[-1, 1], gr.points[0, 1]], 
                           [gr.points[-1, 0], gr.points[0, 0]], 'lime', linewidth=2, alpha=0.9)
        
        ax.set_title(f"{title_prefix}{title}")
        ax.axis('off')
        
        # Add colorbar for normalized images
        if 'normalized' in step:
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    
    # Add legend if grasps are shown
    if has_grasps:
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], color='red', lw=2, label='Original Grasps'),
            Line2D([0], [0], color='lime', lw=2, label='Final Grasps')
        ]
        fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), ncol=2)
    
    plt.tight_layout()
    if has_grasps:
        plt.subplots_adjust(bottom=0.1)
    plt.show()


def visualize_grasp_outputs(results, title_prefix=""):
    """
    Visualize the final grasp outputs (position, angle, width) as generated by the network.
    This shows how the 4-corner grasp rectangles are converted into 3 training images.
    
    Args:
        results (dict): Dictionary containing processed results
        title_prefix (str): Prefix for plot titles
    """
    if not results.get('grasp_info') or not results['grasp_info'].get('final_grasps'):
        print("No grasp information available for output visualization")
        return
    
    # Get the final processed image
    if title_prefix == "Depth " and 'normalized_early' in results:
        final_img = results['resized']
        final_step = 'resized'
    else:
        final_img = results['normalized']
        if len(final_img.shape) == 3 and final_img.shape[0] == 3:
            final_img = final_img.transpose((1, 2, 0))
        final_step = 'normalized'
    
    # Get the processed grasp rectangles
    final_grasps = results['grasp_info']['final_grasps']
    output_size = results['grasp_info']['output_size']
    
    # Generate grasp outputs as done in training
    pos_img, ang_img, width_img = final_grasps.draw((output_size, output_size))
    width_img_normalized = np.clip(width_img, 0.0, output_size / 2) / (output_size / 2)
    
    # Generate cos/sin components as used in actual training
    cos_img = np.cos(2 * ang_img)
    sin_img = np.sin(2 * ang_img)
    
    # Create visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Original image with grasp rectangles
    ax = axes[0, 0]
    if title_prefix == "Depth ":
        ax.imshow(final_img, cmap='viridis')
    else:
        ax.imshow(final_img)
    
    # Overlay grasp rectangles
    for gr in final_grasps:
        ax.plot(gr.points[:, 1], gr.points[:, 0], 'lime', linewidth=2, alpha=0.9)
        ax.plot([gr.points[-1, 1], gr.points[0, 1]], 
               [gr.points[-1, 0], gr.points[0, 0]], 'lime', linewidth=2, alpha=0.9)
    ax.set_title(f'{title_prefix}Final Image + Grasp Rectangles')
    ax.axis('off')
    
    # Position output (Grasp Quality)
    ax = axes[0, 1]
    im_pos = ax.imshow(pos_img, cmap='jet', vmin=0, vmax=1)
    ax.set_title('Position Map (Quality)\n1.0 = Valid Grasp, 0.0 = Invalid')
    ax.axis('off')
    plt.colorbar(im_pos, ax=ax, fraction=0.046, pad=0.04)
    
    # Angle output (raw angle values)
    ax = axes[0, 2]
    im_ang = ax.imshow(ang_img, cmap='hsv', vmin=-np.pi/2, vmax=np.pi/2)
    ax.set_title('Angle Map (Raw)\nRadians [-π/2, π/2]')
    ax.axis('off')
    plt.colorbar(im_ang, ax=ax, fraction=0.046, pad=0.04)
    
    # Cosine component (as used in training)
    ax = axes[1, 0]
    im_cos = ax.imshow(cos_img, cmap='RdBu', vmin=-1, vmax=1)
    ax.set_title('Cosine Component\ncos(2×angle)')
    ax.axis('off')
    plt.colorbar(im_cos, ax=ax, fraction=0.046, pad=0.04)
    
    # Sine component (as used in training)
    ax = axes[1, 1]
    im_sin = ax.imshow(sin_img, cmap='RdBu', vmin=-1, vmax=1)
    ax.set_title('Sine Component\nsin(2×angle)')
    ax.axis('off')
    plt.colorbar(im_sin, ax=ax, fraction=0.046, pad=0.04)
    
    # Width output (normalized)
    ax = axes[1, 2]
    im_width = ax.imshow(width_img_normalized, cmap='jet', vmin=0, vmax=1)
    ax.set_title('Width Map (Normalized)\n0.0 = Min Width, 1.0 = Max Width')
    ax.axis('off')
    plt.colorbar(im_width, ax=ax, fraction=0.046, pad=0.04)
    
    # Add overall title
    fig.suptitle(f'{title_prefix}Grasp Rectangle → Training Images Conversion\n'
                 f'4 Corner Points → 3 Output Maps (Position, Angle, Width)', 
                 fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.show()
    
    # Print statistics
    print(f"\n📊 {title_prefix}Grasp Output Statistics:")
    print(f"   • Number of grasp rectangles: {len(final_grasps.grs)}")
    print(f"   • Position map: {np.sum(pos_img > 0)} pixels marked as valid grasps")
    
    # Only calculate stats for regions with valid grasps
    valid_mask = pos_img > 0
    if np.sum(valid_mask) > 0:
        print(f"   • Angle range: [{np.min(ang_img[valid_mask]):.3f}, {np.max(ang_img[valid_mask]):.3f}] radians")
        print(f"   • Width range: [{np.min(width_img_normalized[valid_mask]):.3f}, {np.max(width_img_normalized[valid_mask]):.3f}] (normalized)")
    else:
        print(f"   • No valid grasp regions found")
    print(f"   • Output size: {output_size}×{output_size} pixels")


def main():
    parser = argparse.ArgumentParser(description="Visualize image preprocessing pipeline")
    parser.add_argument("--rgb_path", type=str, required=True, help="Path to RGB image")
    parser.add_argument("--depth_path", type=str, required=True, help="Path to depth image")
    parser.add_argument("--output_size", type=int, default=224, help="Output size for images")
    parser.add_argument("--rotation", type=float, default=0.0, help="Rotation angle in radians (default: 0.0)")
    parser.add_argument("--zoom", type=float, default=1.0, help="Zoom factor 0.5-1.0 (default: 1.0)")
    parser.add_argument("--use_real_params", action="store_true", default=True, 
                        help="Use real grasp-based crop parameters (default: True)")
    parser.add_argument("--use_fixed_params", action="store_true", default=False,
                        help="Use fixed crop parameters instead of grasp-based ones")
    parser.add_argument("--show_grasps", action="store_true", default=True,
                        help="Show grasp rectangles overlay (default: True)")
    parser.add_argument("--show_outputs", action="store_true", default=False,
                        help="Show final grasp outputs (position, angle, width)")
    
    args = parser.parse_args()
    
    # Handle parameter selection
    use_real_params = args.use_real_params and not args.use_fixed_params
    
    print("=" * 80)
    print("Image Preprocessing Visualization - Cornell Grasping Dataset")
    print("=" * 80)
    
    if use_real_params:
        print("✓ Using REAL training parameters:")
        print("  • Crop position: Based on grasp annotation centers")
        print("  • Rotation: As specified (training uses random rotations: 0°, 90°, 180°, 270°)")
        print("  • Zoom factor: As specified (training uses random zoom: 0.5-1.0)")
        print("  • Grasp rectangles: Loaded from annotation files and transformed")
    else:
        print("✓ Using FIXED parameters for demonstration:")
        print("  • Crop position: Fixed at (100,100)")
        print("  • Rotation: As specified")
        print("  • Zoom factor: As specified")
        print("  • Grasp rectangles: Not available with fixed parameters")
    
    print(f"  • Current rotation: {args.rotation:.2f} radians ({args.rotation * 180 / np.pi:.1f}°)")
    print(f"  • Current zoom: {args.zoom:.2f}x")
    print("  • Processing order: Load → Rotate → Crop → [Normalize*] → Zoom → Resize → [Normalize**]")
    print("    *Depth images normalized before zoom/resize")
    print("    **RGB images normalized after resize + converted to CHW format")
    print("=" * 80)
    
    # Check if files exist
    if not os.path.exists(args.rgb_path):
        print(f"❌ RGB image not found: {args.rgb_path}")
        return
    
    if not os.path.exists(args.depth_path):
        print(f"❌ Depth image not found: {args.depth_path}")
        return
    
    # Process RGB image
    print("\n🖼️  Processing RGB image...")
    rgb_results = load_and_preprocess_image(
        args.rgb_path, 
        is_depth=False, 
        output_size=args.output_size,
        rotation=args.rotation,
        zoom_factor=args.zoom,
        use_real_params=use_real_params
    )
    
    if args.show_grasps:
        visualize_preprocessing(rgb_results, "RGB ")
    
    if args.show_outputs:
        visualize_grasp_outputs(rgb_results, "RGB ")
    
    # Process Depth image
    print("\n📊 Processing Depth image...")
    depth_results = load_and_preprocess_image(
        args.depth_path, 
        is_depth=True, 
        output_size=args.output_size,
        rotation=args.rotation,
        zoom_factor=args.zoom,
        use_real_params=use_real_params
    )
    
    if args.show_grasps:
        visualize_preprocessing(depth_results, "Depth ")
    
    if args.show_outputs:
        visualize_grasp_outputs(depth_results, "Depth ")


if __name__ == "__main__":
    main()
