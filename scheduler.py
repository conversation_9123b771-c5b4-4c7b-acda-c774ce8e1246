#!/usr/bin/env python3
"""
GPU多任务调度器 - 支持单GPU多模型训练
核心原则：
1. 精确的显存管理
2. 进程级别的隔离
3. 显式的资源分配
"""

import subprocess
import time
import os
import signal
import sys
import json
from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Set
from multiprocessing import Process, Queue, Lock, Manager
import logging

@dataclass
class GPUInfo:
    """GPU详细状态"""
    index: int
    memory_used: int
    memory_total: int
    memory_free: int
    processes: List[int] = field(default_factory=list)  # 运行中的进程PID
    
    def can_fit(self, required_memory: int) -> bool:
        """检查是否能容纳新任务"""
        # 保留10%的缓冲区
        buffer = int(self.memory_total * 0.1)
        return self.memory_free > (required_memory + buffer)

@dataclass
class TrainingJob:
    """训练任务定义"""
    dataset: str
    dataset_path: str
    network: str
    description: str
    estimated_memory: int  # 预估显存需求（MB）
    gpu_id: Optional[int] = None
    process: Optional[subprocess.Popen] = None
    start_time: Optional[datetime] = None
    pid: Optional[int] = None

# 网络模型的典型显存需求（根据经验值）
MEMORY_REQUIREMENTS = {
    'yola_grasp_net_v4': 7000,  # 6GB
    'grconvnet3': 7000,         # 4GB
    'senet': 7000,              # 5GB
    'grasp-transformer': 8000,  # 8GB
    'dsnet': 7000,              # 7GB
}

class GPUMultiplexScheduler:
    """
    支持GPU显存复用的调度器
    核心功能：
    1. 精确追踪每个GPU的显存使用
    2. 支持单GPU运行多个模型
    3. 防止显存溢出
    """
    
    def __init__(self, max_jobs_per_gpu: int = 4, log_dir: Optional[str] = None):
        """
        Args:
            max_jobs_per_gpu: 每个GPU最多运行的任务数
        """
        self.max_jobs_per_gpu = max_jobs_per_gpu
        self.log_dir = log_dir or self._create_log_dir()
        self._setup_logging()
        
        # 使用Manager创建跨进程共享的数据结构
        manager = Manager()
        self.gpu_jobs = manager.dict()  # GPU_ID -> List[Job]
        self.job_registry = manager.dict()  # PID -> Job
        self.lock = manager.Lock()
        
        self._setup_signal_handlers()
        self._init_gpu_state()
    
    def _create_log_dir(self) -> str:
        """创建日志目录"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_dir = f"logs/{timestamp}_multiplex_training"
        os.makedirs(log_dir, exist_ok=True)
        return log_dir
    
    def _setup_logging(self):
        """配置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(process)d] - %(message)s',
            handlers=[
                logging.FileHandler(f"{self.log_dir}/scheduler.log"),
                logging.StreamHandler()
            ]
        )
    
    def _setup_signal_handlers(self):
        """信号处理"""
        def cleanup(signum, frame):
            logging.info("Received interrupt, cleaning up all jobs...")
            self.cleanup_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, cleanup)
        signal.signal(signal.SIGTERM, cleanup)
    
    def _init_gpu_state(self):
        """初始化GPU状态"""
        gpus = self.get_gpu_status()
        for gpu in gpus:
            self.gpu_jobs[gpu.index] = []
        logging.info(f"Initialized {len(gpus)} GPUs for multiplexing")
    
    def get_gpu_status(self) -> List[GPUInfo]:
        """获取详细的GPU状态，包括进程信息"""
        try:
            # 查询GPU基本信息
            result = subprocess.run(
                ['nvidia-smi', '--query-gpu=index,memory.used,memory.total,memory.free',
                 '--format=csv,noheader,nounits'],
                capture_output=True, text=True, check=True
            )
            
            gpus = []
            for line in result.stdout.strip().split('\n'):
                parts = line.split(', ')
                gpu = GPUInfo(
                    index=int(parts[0]),
                    memory_used=int(parts[1]),
                    memory_total=int(parts[2]),
                    memory_free=int(parts[3])
                )
                
                # 获取该GPU上运行的进程
                gpu.processes = self._get_gpu_processes(gpu.index)
                gpus.append(gpu)
            
            return gpus
            
        except subprocess.CalledProcessError as e:
            logging.error(f"Failed to query GPU status: {e}")
            return []
    
    def _get_gpu_processes(self, gpu_id: int) -> List[int]:
        """获取指定GPU上的进程列表"""
        try:
            result = subprocess.run(
                ['nvidia-smi', f'--id={gpu_id}', '--query-compute-apps=pid',
                 '--format=csv,noheader'],
                capture_output=True, text=True, check=True
            )
            
            pids = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    pids.append(int(line))
            return pids
            
        except:
            return []
    
    def find_best_gpu_for_job(self, job: TrainingJob) -> Optional[int]:
        """为任务找到最合适的GPU"""
        gpus = self.get_gpu_status()
        
        best_gpu = None
        best_score = -1
        
        for gpu in gpus:
            # 检查是否能容纳新任务
            if not gpu.can_fit(job.estimated_memory):
                continue
            
            # 检查是否达到最大任务数限制
            with self.lock:
                current_jobs = len(self.gpu_jobs.get(gpu.index, []))
                if current_jobs >= self.max_jobs_per_gpu:
                    continue
            
            # 计算适合度分数
            # 优先选择：1) 已有任务少的 2) 剩余显存多的
            jobs_score = (self.max_jobs_per_gpu - current_jobs) / self.max_jobs_per_gpu
            memory_score = gpu.memory_free / gpu.memory_total
            score = jobs_score * 0.6 + memory_score * 0.4
            
            if score > best_score:
                best_score = score
                best_gpu = gpu.index
        
        return best_gpu
    
    def run_job(self, job: TrainingJob) -> bool:
        """运行单个训练任务"""
        # 等待合适的GPU
        max_wait = 3600
        start_wait = time.time()
        
        while time.time() - start_wait < max_wait:
            gpu_id = self.find_best_gpu_for_job(job)
            
            if gpu_id is not None:
                # 分配GPU并启动任务
                return self._start_job_on_gpu(job, gpu_id)
            
            # 显示当前状态并等待
            time.sleep(30)
        
        logging.error(f"Timeout waiting for GPU for {job.description}")
        return False
    
    def _start_job_on_gpu(self, job: TrainingJob, gpu_id: int) -> bool:
        """在指定GPU上启动任务"""
        cmd = [
            'python', 'train_network2.py',
            '--dataset', job.dataset,
            '--dataset-path', job.dataset_path,
            '--network', job.network,
            '--description', job.description
        ]
        
        # 设置环境变量
        env = os.environ.copy()
        env['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        
        # 日志文件
        log_file = f"{self.log_dir}/{job.description}_gpu{gpu_id}.log"
        
        try:
            # 启动进程
            with open(log_file, 'w') as f:
                process = subprocess.Popen(
                    cmd, env=env,
                    stdout=f, stderr=subprocess.STDOUT,
                    preexec_fn=os.setsid  # 创建新进程组
                )
            
            # 更新任务信息
            job.gpu_id = gpu_id
            job.process = process
            job.pid = process.pid
            job.start_time = datetime.now()
            
            # 注册任务
            with self.lock:
                if gpu_id not in self.gpu_jobs:
                    self.gpu_jobs[gpu_id] = []
                jobs_list = list(self.gpu_jobs[gpu_id])
                jobs_list.append(job)
                self.gpu_jobs[gpu_id] = jobs_list
                self.job_registry[process.pid] = job
            
            
            # 等待进程完成
            return_code = process.wait()
            
            # 清理任务记录
            with self.lock:
                jobs_list = list(self.gpu_jobs[gpu_id])
                jobs_list = [j for j in jobs_list if j.pid != process.pid]
                self.gpu_jobs[gpu_id] = jobs_list
                
                if process.pid in self.job_registry:
                    del self.job_registry[process.pid]
            
            if return_code == 0:
                logging.info(f"Experiment completed: {job.description}")
                return True
            else:
                logging.error(f"✗ Failed {job.description} with code {return_code}")
                return False
                
        except Exception as e:
            logging.error(f"Error running {job.description}: {e}")
            return False
    
    def _show_detailed_status(self):
        """显示详细的GPU和任务状态"""
        gpus = self.get_gpu_status()
        
        logging.info("=" * 70)
        logging.info("GPU STATUS REPORT")
        logging.info("=" * 70)
        
        for gpu in gpus:
            with self.lock:
                jobs = self.gpu_jobs.get(gpu.index, [])
            
            logging.info(
                f"GPU {gpu.index}: {gpu.memory_used}/{gpu.memory_total}MB used "
                f"({gpu.memory_free}MB free) | {len(jobs)} jobs running"
            )
            
            for job in jobs:
                if job.start_time:
                    runtime = datetime.now() - job.start_time
                    logging.info(
                        f"  └─ {job.network} (PID: {job.pid}, "
                        f"Runtime: {runtime}, Est: {job.estimated_memory}MB)"
                    )
    
    def cleanup_all(self):
        """清理所有运行中的任务"""
        with self.lock:
            for gpu_id, jobs in self.gpu_jobs.items():
                for job in jobs:
                    if job.process and job.process.poll() is None:
                        logging.info(f"Terminating {job.description} (PID: {job.pid})")
                        try:
                            os.killpg(os.getpgid(job.pid), signal.SIGTERM)
                        except:
                            pass
    
    def run_experiments_multiplexed(self, experiments: List[Dict]) -> None:
        """使用GPU复用运行所有实验"""
        total = len(experiments)
        logging.info(f"Starting {total} experiments with GPU multiplexing")
        logging.info(f"Max {self.max_jobs_per_gpu} jobs per GPU")
        
        # 创建任务队列
        job_queue = Queue()
        
        for exp in experiments:
            job = TrainingJob(
                dataset=exp['dataset'],
                dataset_path=exp['dataset_path'],
                network=exp['network'],
                description=exp['description'],
                estimated_memory=MEMORY_REQUIREMENTS.get(exp['network'], 8000)
            )
            job_queue.put(job)
        
        # 工作进程函数
        def worker():
            while not job_queue.empty():
                try:
                    job = job_queue.get(timeout=1)
                    self.run_job(job)
                except:
                    break
        
        # 启动工作进程
        # 进程数 = GPU数量 * 每GPU最大任务数
        num_gpus = len(self.get_gpu_status())
        num_workers = min(num_gpus * self.max_jobs_per_gpu, total)
        
        logging.info(f"Starting {num_workers} worker processes")
        
        processes = []
        for i in range(num_workers):
            p = Process(target=worker)
            p.start()
            processes.append(p)
            time.sleep(5)  # 错开启动时间，避免竞争
        
        # 监控进程
        monitor_process = Process(target=self._monitor_loop)
        monitor_process.start()
        
        # 等待所有工作进程完成
        for p in processes:
            p.join()
        
        # 停止监控
        monitor_process.terminate()
        
        logging.info("All experiments completed")
    
    def _monitor_loop(self):
        """监控循环 - 在独立进程中运行"""
        while True:
            time.sleep(1800)  # 每30分钟更新一次
            self._show_detailed_status()
            self._cleanup_finished_jobs()
    
    def _cleanup_finished_jobs(self):
        """清理已完成的任务记录"""
        with self.lock:
            for gpu_id in list(self.gpu_jobs.keys()):
                jobs = self.gpu_jobs[gpu_id]
                active_jobs = []
                
                for job in jobs:
                    if job.process and job.process.poll() is None:
                        active_jobs.append(job)
                    else:
                        logging.info(f"Cleaned up finished job: {job.description}")
                
                self.gpu_jobs[gpu_id] = active_jobs


def main():
    """主函数"""
    
    # 清理可能的僵尸进程
    logging.info("Cleaning up any zombie processes...")
    subprocess.run(['pkill', '-f', 'train_network2.py'], stderr=subprocess.DEVNULL)
    time.sleep(2)
    
    # 实验配置
    experiments = [
        {
            'dataset': 'cornell',
            'dataset_path': '/home/<USER>/datasets/cornell-with-low-light',
            'network': 'yola_grasp_net_v4',
            'description': 'training_very_dark_cornell_with_yola_grasp_net_v4'
        },
        {
            'dataset': 'cornell',
            'dataset_path': '/home/<USER>/datasets/cornell-with-low-light',
            'network': 'grconvnet3',
            'description': 'training_very_dark_cornell_with_grconvnet3'
        },
        {
            'dataset': 'cornell',
            'dataset_path': '/home/<USER>/datasets/cornell-with-low-light',
            'network': 'senet',
            'description': 'training_very_dark_cornell_with_senet'
        },
        {
            'dataset': 'cornell',
            'dataset_path': '/home/<USER>/datasets/cornell-with-low-light',
            'network': 'grasp-transformer',
            'description': 'training_very_dark_cornell_with_grasp-transformer'
        },
        {
            'dataset': 'cornell',
            'dataset_path': '/home/<USER>/datasets/cornell-with-low-light',
            'network': 'dsnet',
            'description': 'training_very_dark_cornell_with_dsnet'
        }
    ]
    
    # 创建调度器
    # 根据你的GPU显存大小调整max_jobs_per_gpu
    # 例如：32GB GPU可以同时运行3-4个小模型
    scheduler = GPUMultiplexScheduler(max_jobs_per_gpu=3)
    
    # 运行实验
    scheduler.run_experiments_multiplexed(experiments)


if __name__ == "__main__":
    main()