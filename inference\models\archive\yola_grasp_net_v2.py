import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# ===============================================================
# 0. ReflectedConvolution 是原始YOLA论文中IIM的核心实现
# ===============================================================

class ReflectedConvolution(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3):
        super(ReflectedConvolution, self).__init__()
        self.kernel_size = kernel_size
        self.in_channels = in_channels # 通常是1 (处理单个log(color)通道)
        self.out_channels = out_channels # 希望输出的特征通道数

        # 为每个颜色对创建一个独立的BN层
        self.rg_bn = nn.BatchNorm2d(out_channels)
        self.gb_bn = nn.BatchNorm2d(out_channels)
        self.rb_bn = nn.BatchNorm2d(out_channels)
        
        # 可学习的卷积核
        self.filter = nn.Parameter(torch.randn(self.out_channels, self.in_channels, self.kernel_size, self.kernel_size))
        
        self.init_weights()

    def init_weights(self):
        torch.nn.init.kaiming_normal_(self.filter, mode='fan_out', nonlinearity='relu')
        
    def mean_constraint(self, kernel):
        # 强制卷积核权重均值为0，这是实现光照不变性的关键
        mean = torch.mean(kernel, dim=[1, 2, 3], keepdim=True)
        return kernel - mean

    def forward(self, img):
        # 对输入图像取对数，并处理0值防止log(0)
        log_img = torch.log(torch.clamp(img, min=1e-6))

        red_chan = log_img[:, 0:1, :, :]
        green_chan = log_img[:, 1:2, :, :]
        blue_chan = log_img[:, 2:3, :, :]
        
        normalized_filter = self.mean_constraint(self.filter)

        # Red-Green
        filt_r1 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_g1 = F.conv2d(green_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rg = self.rg_bn(filt_r1 + filt_g1)

        # Green-Blue
        filt_g2 = F.conv2d(green_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b1 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_gb = self.gb_bn(filt_g2 + filt_b1)

        # Red-Blue
        filt_r2 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b2 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rb = self.rb_bn(filt_r2 + filt_b2)
        
        # 将三个颜色对的特征拼接起来
        return torch.cat([filt_rg, filt_gb, filt_rb], dim=1)

# ===============================================================
# 1. 基础模块 (Base Modules - Unchanged)
# ===============================================================
class GraspModel(nn.Module):
    def __init__(self):
        super(GraspModel, self).__init__()
    def forward(self, x_in):
        raise NotImplementedError()
    def compute_loss(self, xc, yc):
        y_pos, y_cos, y_sin, y_width = yc
        pred_dict = self.forward(xc)
        pos_pred, cos_pred, sin_pred, width_pred = pred_dict['grasp_predictions'].values()
        grasp_loss = F.smooth_l1_loss(pos_pred, y_pos) + F.smooth_l1_loss(cos_pred, y_cos) + \
                     F.smooth_l1_loss(sin_pred, y_sin) + F.smooth_l1_loss(width_pred, y_width)
        return {'grasp_loss': grasp_loss, 'aux_losses': pred_dict.get('aux_losses', {}),
                'predictions': pred_dict['grasp_predictions'], 'decompositions': pred_dict.get('decompositions', {})}
    def predict(self, xc):
        return self.forward(xc)

class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels: mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1), nn.BatchNorm2d(mid_channels), nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1), nn.BatchNorm2d(out_channels), nn.ReLU(inplace=True))
    def forward(self, x): return self.double_conv(x)

class Down(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(nn.MaxPool2d(2), DoubleConv(in_channels, out_channels))
    def forward(self, x): return self.maxpool_conv(x)

class Up(nn.Module):
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)
    def forward(self, x1, x2):
        x1 = self.up(x1)
        diffY, diffX = x2.size()[2] - x1.size()[2], x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class NoiseDecouplingNetwork(nn.Module):
    def __init__(self, input_channels=3):
        super().__init__()
        self.enc1, self.enc2 = DoubleConv(input_channels, 32), Down(32, 64)
        self.dec1 = Up(64, 32)
        self.out_structure = nn.Conv2d(32, input_channels, kernel_size=1)
        self.out_noise = nn.Conv2d(32, input_channels, kernel_size=1)
    def forward(self, x):
        x1, x2 = self.enc1(x), self.enc2(self.enc1(x))
        x_dec = self.dec1(x2, x1)
        structure_layer, noise_layer = torch.tanh(self.out_structure(x_dec)), self.out_noise(x_dec)
        return structure_layer, noise_layer

class ReflectionSeparationModule(nn.Module):
    def __init__(self, input_channels=3):
        super().__init__()
        self.conv_block = nn.Sequential(DoubleConv(input_channels, 32), DoubleConv(32, 32))
        self.out_diffuse = nn.Conv2d(32, input_channels, kernel_size=1)
        self.out_specular = nn.Conv2d(32, input_channels, kernel_size=1)
    def forward(self, x):
        features = self.conv_block(x)
        diffuse_layer, specular_layer = torch.tanh(self.out_diffuse(features)), F.relu(self.out_specular(features))
        return diffuse_layer, specular_layer

# ===============================================================
# 2. 为抓取任务改进的 IIM-Grasp 模块
# ===============================================================

class GeometricSalienceGate(nn.Module):
    """几何凸显门控，用于抑制纹理边缘，凸显物理边缘。"""
    def __init__(self, input_channels):
        super().__init__()
        self.gate_conv = nn.Sequential(
            nn.Conv2d(input_channels, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 1, kernel_size=1), # 输出单通道的门控图
            nn.Sigmoid() # 将输出压缩到 0-1 范围
        )
    def forward(self, x):
        return self.gate_conv(x)

class IIMGrasp(nn.Module):
    """为抓取任务设计的、多尺度、带门控的光照不变模块。"""
    def __init__(self, input_channels=3, iim_channels=8):
        super().__init__()
        # 1. 多尺度IIM
        self.iim_k3 = ReflectedConvolution(1, iim_channels, kernel_size=3)
        self.iim_k5 = ReflectedConvolution(1, iim_channels, kernel_size=5)
        
        # 2. 几何凸显门控
        # 输入是漫反射层，通道数为 input_channels
        self.gate = GeometricSalienceGate(input_channels)
        
        # 3. 1x1卷积用于融合多尺度特征
        # 总通道数 = 3个颜色对 * (k3通道数 + k5通道数)
        total_iim_channels = 3 * (iim_channels + iim_channels)
        self.fusion_conv = nn.Conv2d(total_iim_channels, total_iim_channels, kernel_size=1)

    def forward(self, diffuse_layer):
        # 多尺度特征提取
        feat_k3 = self.iim_k3(diffuse_layer)
        feat_k5 = self.iim_k5(diffuse_layer)
        
        # 拼接多尺度特征
        multi_scale_feat = torch.cat([feat_k3, feat_k5], dim=1)
        fused_feat = self.fusion_conv(multi_scale_feat)
        
        # 计算并应用几何门控
        salience_gate = self.gate(diffuse_layer)
        gated_feat = fused_feat * salience_gate # 广播机制
        
        return gated_feat

# ===============================================================
# 3. 最终的、多分支融合的抓取模型
# ===============================================================

class YOLAGraspUNetV2(GraspModel):
    """
    集成了预处理、IIM-Grasp和三分支融合的最终模型。
    """
    def __init__(self, input_channels=3, output_channels=1, channel_size=32, bilinear=True):
        super().__init__()
        
        # ------------------- 1. 预处理前端 -------------------
        self.noise_decoupler = NoiseDecouplingNetwork(input_channels)
        self.reflection_separator = ReflectionSeparationModule(input_channels)
        
        # ------------------- 2. 三分支编码器 (Tri-Branch Encoder) -------------------
        cs = channel_size # 简化变量名
        
        # 分支A: RGB/结构分支
        self.rgb_enc1 = DoubleConv(input_channels, cs)
        
        # 分支B: IIM-Grasp/边缘分支
        self.iim_grasp_module = IIMGrasp(input_channels, iim_channels=cs//3) # 输出通道数 맞춰주기
        # IIM输出通道数为 2 * (cs//3) * 3 = 2*cs, 1x1 conv로 cs로 줄여줌
        self.iim_enc1_proj = nn.Conv2d(2 * cs, cs, kernel_size=1)

        # 分支C: 伪几何/形状分支
        self.shape_enc1 = DoubleConv(input_channels, cs)

        # 编码器下采样部分 (共享)
        # 输入通道数是融合后的通道数
        self.down1 = Down(cs * 3, cs * 2)
        self.down2 = Down(cs * 2, cs * 4)
        self.down3 = Down(cs * 4, cs * 8)
        factor = 2 if bilinear else 1
        self.down4 = Down(cs * 8, cs * 16 // factor)

        # ------------------- 3. U-Net 解码器 (Decoder) -------------------
        self.up1 = Up(cs * 16, cs * 8 // factor, bilinear)
        self.up2 = Up(cs * 8, cs * 4 // factor, bilinear)
        self.up3 = Up(cs * 4, cs * 2 // factor, bilinear)
        # 解码器最后一层的输入通道数需要考虑来自编码器第一层的融合特征
        self.up4 = Up(cs * 2, cs * 3, bilinear) # 输出通道数与融合后的第一层encoder特征通道数一致
        
        # 最终特征融合与预测头
        self.final_conv = DoubleConv(cs * 3, cs)
        self.pos_output = nn.Conv2d(cs, output_channels, kernel_size=1)
        self.cos_output = nn.Conv2d(cs, output_channels, kernel_size=1)
        self.sin_output = nn.Conv2d(cs, output_channels, kernel_size=1)
        self.width_output = nn.Conv2d(cs, output_channels, kernel_size=1)

        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.xavier_uniform_(m.weight, gain=1)

    def forward(self, x_in):
        # --- 阶段1: 预处理 ---
        structure_layer, noise_layer = self.noise_decoupler(x_in)
        diffuse_layer, specular_layer = self.reflection_separator(structure_layer)
        
        # --- 阶段2: 三分支编码 ---
        # 编码器第一层 (L1)
        rgb_feat_l1 = self.rgb_enc1(structure_layer)
        iim_feat_l1_raw = self.iim_grasp_module(diffuse_layer)
        iim_feat_l1 = self.iim_enc1_proj(iim_feat_l1_raw)
        shape_feat_l1 = self.shape_enc1(structure_layer)

        # 在第一层融合特征，用于skip connection
        fused_feat_l1 = torch.cat([rgb_feat_l1, iim_feat_l1, shape_feat_l1], dim=1)
        
        # 编码器下采样
        x_l2 = self.down1(fused_feat_l1)
        x_l3 = self.down2(x_l2)
        x_l4 = self.down3(x_l3)
        x_l5 = self.down4(x_l4) # 瓶颈层特征
        
        # --- 阶段3: 解码与特征融合 ---
        x = self.up1(x_l5, x_l4)
        x = self.up2(x, x_l3)
        x = self.up3(x, x_l2)
        x = self.up4(x, fused_feat_l1) # 与第一层融合后的特征进行skip connection
        
        # --- 阶段4: 最终预测 ---
        final_feat = self.final_conv(x)
        pos_output = self.pos_output(final_feat)
        cos_output = self.cos_output(final_feat)
        sin_output = self.sin_output(final_feat)
        width_output = self.width_output(final_feat)
        
        # --- 阶段5: 收集所有输出和辅助损失 ---
        noise_recon_loss = F.l1_loss(structure_layer + noise_layer, x_in)
        reflection_recon_loss = F.l1_loss(diffuse_layer + specular_layer, structure_layer)
        specular_sparsity_loss = torch.mean(torch.abs(specular_layer))

        return {
            'grasp_predictions': {'pos': pos_output, 'cos': cos_output, 'sin': sin_output, 'width': width_output},
            'decompositions': {'structure': structure_layer, 'noise': noise_layer, 'diffuse': diffuse_layer, 'specular': specular_layer},
            'aux_losses': {'noise_recon': noise_recon_loss, 'reflection_recon': reflection_recon_loss, 'specular_sparsity': specular_sparsity_loss}
        }

# ===============================================================
# 4. 使用示例 (Example Usage)
# ===============================================================
if __name__ == '__main__':
    model = YOLAGraspUNetV2(input_channels=3, output_channels=1, channel_size=32)
    dummy_input = torch.randn(2, 3, 224, 224)
    print("输入张量尺寸:", dummy_input.shape)
    
    output_dict = model(dummy_input)
    
    print("\n抓取预测结果:")
    for name, tensor in output_dict['grasp_predictions'].items():
        print(f"  - {name}: {tensor.shape}")
        
    print("\n图像分解结果:")
    for name, tensor in output_dict['decompositions'].items():
        print(f"  - {name}: {tensor.shape}")

    print("\n辅助损失项:")
    for name, loss in output_dict['aux_losses'].items():
        print(f"  - {name}: {loss.item():.4f}")
