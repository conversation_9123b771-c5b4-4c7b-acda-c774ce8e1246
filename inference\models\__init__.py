def get_network(network_name):
    network_name = network_name.lower()
    # Original GR-ConvNet
    if network_name == 'grconvnet':
        from .grconvnet import GenerativeResnet
        return GenerativeResnet
    # Configurable GR-ConvNet with multiple dropouts
    elif network_name == 'grconvnet2':
        from .grconvnet2 import GenerativeResnet
        return GenerativeResnet
    # Configurable GR-ConvNet with dropout at the end
    elif network_name == 'grconvnet3':
        from .grconvnet3 import GenerativeResnet
        return GenerativeResnet
    # Inverted GR-ConvNet
    elif network_name == 'grconvnet4':
        from .grconvnet4 import GenerativeResnet
        return GenerativeResnet
    elif network_name == 'grasp-transformer':
        from .experiments.swin import SwinTransformerSys
        return SwinTransformerSys
    elif network_name == "senet":
        from .senetgrasp import SEResUNet
        return SEResUNet
    elif network_name == "skgnet":
        from .experiments.skgnet import SKNet
        return SKNet
    elif network_name == "dsnet":
        from .experiments.DSNet import DSNetSys
        return DSNetSys
    elif network_name == 'hgnet-grasp':
        from .experiments.HFNet import HGNet
        return HGNet
    elif network_name == 'yola_grasp_net_v4':
        from .YOLAGraspUnet_v4 import YOLAGraspUNetV4
        return YOLAGraspUNetV4
    elif network_name == 'graspnet':
        from .net2 import GraspNet
        return GraspNet
    else:
        raise NotImplementedError('Network {} is not implemented'.format(network_name))
