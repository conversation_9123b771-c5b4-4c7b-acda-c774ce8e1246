# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview
This is a robotic grasping system using Generative Residual Convolutional Neural Networks (GR-ConvNet) and other architectures to detect objects and predict antipodal grasp configurations from RGB-D images. The system supports both Cornell and Jacquard datasets and includes training, evaluation, and real-time inference capabilities with Intel RealSense camera integration.

## Architecture
- **Core Models**: Multiple variants including GR-ConvNet (grconvnet, grconvnet2, grconvnet3, grconvnet4), U-Net, SE-Net, SKGNet, DSNet, and custom YOLA-Grasp architectures
- **Input**: RGB-D images (224x224 by default, 300x300 for Jacquard)
- **Output**: Grasp quality map, angle map, width map for detecting optimal grasp positions
- **Device Support**: CUDA GPU with fallback to CPU

## Key Components

### Core Modules
- `inference/models/` - Model definitions (GR-ConvNet variants, U-Net, Transformer-based models)
- `utils/data/` - Dataset loaders for Cornell and Jacquard datasets
- `utils/dataset_processing/` - Image processing and grasp evaluation utilities
- `hardware/` - Camera interface (RealSense) and device management
- `inference/post_process.py` - Post-processing of model outputs

### Training Pipeline
- `train_network.py` - Main training script with validation and checkpointing
- `evaluate.py` - Model evaluation with IoU metrics
- Automatic checkpointing every 10 epochs and for best IoU performance
- TensorBoard logs in `logs/` directory (created during training)

### Inference Modes
- `run_offline.py` - Single image inference from file
- `run_realtime.py` - Real-time camera inference with visualization
- `run_grasp_generator.py` - Production grasp generator with ROS integration
- `run_calibration.py` - Camera calibration utility

## Commands

### Setup & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Download datasets
bash utils/get_cornell.sh
bash utils/get_jacquard.sh

# Generate depth images for Cornell dataset
python -m utils.dataset_processing.generate_cornell_depth <dataset_path>
```

### Training
```bash
# Train on Cornell dataset (recommended model: yola_grasp_net_v4)
python train_network.py --dataset cornell --dataset-path <path> --description training_cornell --network yola_grasp_net_v4

# Train on Jacquard dataset
python train_network.py --dataset jacquard --dataset-path <path> --description training_jacquard --use-dropout 0 --input-size 300 --network grconvnet3

# Key training parameters:
# --network: Model type (yola_grasp_net_v4, grconvnet3, unet, etc.)
# --input-size: Image size (224 for Cornell, 300 for Jacquard)
# --use-depth/--use-rgb: Enable depth/RGB input channels
# --channel-size: Network width (16/32)
# --epochs: Number of training epochs (default 100)
# --batch-size: Training batch size (default 32)
```

### Evaluation
```bash
# Evaluate trained model
python evaluate.py --network <path_to_model> --dataset cornell --dataset-path <path> --iou-eval

# Offline inference on single image
python run_offline.py --network <path_to_model> --rgb_path <image.png> --depth_path <depth.tiff>
```

### Real-time Usage
```bash
# Real-time camera inference
python run_realtime.py --network <path_to_model>

# Production grasp generator (with ROS)
python run_grasp_generator.py --network <path_to_model> --camera <camera_id>
```

### Model Management
```bash
# Cleanup small training logs
./cleanup.sh

# Trained models are saved in timestamped directories under logs/
# Look for directories with format: <timestamp>_ Cornell-randsplit-rgbd-<model>-drop<dropout>-ch<channel>/
```

## Key Files & Directories
- `train_network.py:208-345` - Main training loop with validation
- `inference/models/grconvnet3.py:51-75` - Core GR-ConvNet forward pass
- `inference/models/__init__.py` - Model registry with all available architectures
- `utils/data/cornell_data.py:42-70` - Cornell dataset processing
- `hardware/camera.py` - RealSense camera interface
- `inference/grasp_generator.py:50-106` - Production grasp generation loop
- `inference/models/yola_grasp_net_v3.py` - Custom YOLA-Grasp architecture (default in train_network.py)

## Testing & Validation
- IoU threshold: 0.25 (default) for grasp evaluation
- Model checkpoints saved every 10 epochs and for best IoU
- TensorBoard logs in `logs/` directory
- Evaluation metrics: IoU-based success rate, grasp detection accuracy

## Hardware Requirements
- NVIDIA GPU with CUDA support (recommended)
- Intel RealSense camera (D435/D415) for real-time inference
- Python 3.6+ with PyTorch