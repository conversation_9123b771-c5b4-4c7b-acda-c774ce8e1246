import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange

try:
    from .grasp_model import GraspModel
except ImportError:
    # For standalone execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from grasp_model import GraspModel

# ===============================================================
# 0. Basic Building Blocks
# ===============================================================

class ConvBlock(nn.Module):
    """Dual Convolution Block: (Conv2D => BatchNorm => SiLU) * 2"""
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.SiLU(inplace=False), # 使用 SiLU 并设置 inplace=False 以确保安全
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(inplace=False)
        )
    def forward(self, x):
        return self.double_conv(x)

class DownSample(nn.Module):
    """Downsampling: MaxPool + ConvBlock"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            ConvBlock(in_channels, out_channels)
        )
    def forward(self, x):
        return self.maxpool_conv(x)

class UpSample(nn.Module):
    """Upsampling: Upsample + ConvBlock"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = ConvBlock(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = ConvBlock(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        # Pad x1 to the size of x2
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

# ===============================================================
# 1. Cross-Modal Attention Fusion
# ===============================================================

class CrossModalAttention(nn.Module):
    """Cross-attention module for fusing RGB, depth, and illumination features"""
    def __init__(self, channels, num_heads=8):
        super().__init__()
        self.num_heads = num_heads
        self.scale = (channels // num_heads) ** -0.5

        # Projections for Q, K, V for each modality
        self.to_q = nn.ModuleDict({
            'rgb': nn.Conv2d(channels, channels, 1, bias=False),
            'iim': nn.Conv2d(channels, channels, 1, bias=False),
            'depth': nn.Conv2d(channels, channels, 1, bias=False)
        })
        self.to_kv = nn.ModuleDict({
            'rgb': nn.Conv2d(channels, channels * 2, 1, bias=False),
            'iim': nn.Conv2d(channels, channels * 2, 1, bias=False),
            'depth': nn.Conv2d(channels, channels * 2, 1, bias=False)
        })

        self.proj = nn.Conv2d(channels * 3, channels * 3, 1) # Final projection

    def forward(self, rgb_feat, iim_feat, depth_feat):
        modalities = {'rgb': rgb_feat, 'iim': iim_feat, 'depth': depth_feat}
        attended_modalities = {}

        for name, feat in modalities.items():
            # Generate Query for the current modality
            q = self.to_q[name](feat)
            q = rearrange(q, 'b (h d) x y -> b h (x y) d', h=self.num_heads)

            # Generate Keys and Values from other modalities
            other_kvs = [self.to_kv[n](f) for n, f in modalities.items() if n != name]
            k, v = rearrange(other_kvs, 'modal b (kv h d) x y -> modal b kv h (x y) d', kv=2, h=self.num_heads).unbind(dim=2)
            
            # Merge keys and values from the two other sources
            k = rearrange(k, 'modal b h n d -> b h (modal n) d')
            v = rearrange(v, 'modal b h n d -> b h (modal n) d')

            # Attention mechanism
            dots = torch.einsum('b h i d, b h j d -> b h i j', q, k) * self.scale
            attn = dots.softmax(dim=-1)
            out = torch.einsum('b h i j, b h j d -> b h i d', attn, v)
            
            # Reconstruct the feature map and add residual connection
            out = rearrange(out, 'b h (x y) d -> b (h d) x y', x=feat.shape[2], y=feat.shape[3])
            attended_modalities[name] = feat + out # Residual connection

        # Concatenate the refined features from all modalities
        fused = torch.cat(list(attended_modalities.values()), dim=1)
        return self.proj(fused)

# ===============================================================
# 2. Multi-Modal Feature Encoder
# ===============================================================

# --- 2.1 Image Preprocessing Modules ---
class ImageDenoiser(nn.Module):
    """Separates image structure from noise"""
    def __init__(self, in_channels=3, feat_channels=24):
        super().__init__()
        self.enc1 = ConvBlock(in_channels, feat_channels)
        self.enc2 = DownSample(feat_channels, feat_channels * 2)
        self.dec1 = UpSample(feat_channels * 2 + feat_channels, feat_channels, bilinear=True)
        self.out_structure = nn.Conv2d(feat_channels, in_channels, 1)
        self.out_noise = nn.Conv2d(feat_channels, in_channels, 1)
    def forward(self, x):
        x1 = self.enc1(x)
        x2 = self.enc2(x1)
        x_dec = self.dec1(x2, x1)
        structure = torch.tanh(self.out_structure(x_dec))
        noise = self.out_noise(x_dec)
        return structure, noise

class ReflectionSeparator(nn.Module):
    """Separates diffuse and specular reflection components"""
    def __init__(self, in_channels=3, feat_channels=24):
        super().__init__()
        self.conv_block = nn.Sequential(ConvBlock(in_channels, feat_channels), ConvBlock(feat_channels, feat_channels))
        self.out_diffuse = nn.Conv2d(feat_channels, in_channels, 1)
        self.out_specular = nn.Conv2d(feat_channels, in_channels, 1)
    def forward(self, x):
        features = self.conv_block(x)
        diffuse = torch.tanh(self.out_diffuse(features))
        specular = F.relu(self.out_specular(features))
        return diffuse, specular

# --- 2.2 Illumination Invariant Feature Extractor ---
class IlluminationExtractor(nn.Module):
    """Extracts illumination-invariant features using color ratio convolutions"""
    def __init__(self, in_channels, out_channels, kernel_size=3):
        super().__init__()
        self.padding = kernel_size // 2
        self.filter = nn.Parameter(torch.randn(out_channels, in_channels, kernel_size, kernel_size))
        self.bn = nn.ModuleDict({
            'rg': nn.BatchNorm2d(out_channels), 'gb': nn.BatchNorm2d(out_channels), 'rb': nn.BatchNorm2d(out_channels)
        })
        nn.init.kaiming_normal_(self.filter, mode='fan_out', nonlinearity='relu')
        
    def forward(self, img):
        log_img = torch.log1p(12.0 * img.clamp(min=0.0))
        r, g, b = log_img.split(1, dim=1)
        w = self.filter - self.filter.mean(dim=[1, 2, 3], keepdim=True)

        # Split the filter weights for each channel
        w_r, w_g, w_b = w.split(1, dim=1)  # Split along input channel dimension

        r_pos = F.conv2d(r, w_r, padding=self.padding)
        g_pos = F.conv2d(g, w_g, padding=self.padding)
        b_pos = F.conv2d(b, w_b, padding=self.padding)

        r_neg = F.conv2d(r, -w_r, padding=self.padding)
        g_neg = F.conv2d(g, -w_g, padding=self.padding)
        b_neg = F.conv2d(b, -w_b, padding=self.padding)

        filt_rg = self.bn['rg'](r_pos + g_neg)
        filt_gb = self.bn['gb'](g_pos + b_neg)
        filt_rb = self.bn['rb'](r_pos + b_neg)
        return torch.cat([filt_rg, filt_gb, filt_rb], dim=1)

# --- 2.3 Multi-Modal Feature Encoder ---
class FeatureEncoder(nn.Module):
    """Multi-modal feature encoder with cross-attention fusion"""
    def __init__(self, channel_size=32):
        super().__init__()
        cs = channel_size

        # RGB Pre-processing
        self.image_denoiser = ImageDenoiser(3, 24)
        self.reflection_separator = ReflectionSeparator(3, 24)

        # Initial Encoders for each branch
        self.rgb_encoder = ConvBlock(3, cs)
        self.depth_encoder = ConvBlock(cs, cs)

        # Geometric fusion block
        self.geo_fusion_conv = nn.Sequential(
            nn.Conv2d(1 + 3, cs, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(cs),
            nn.SiLU(),
            nn.Conv2d(cs, cs, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(cs),
            nn.SiLU()
        )

        # IIM Branch  
        iim_channels = cs // 4
        self.iim_extractor_k3 = IlluminationExtractor(3, iim_channels, 3)
        self.iim_extractor_k5 = IlluminationExtractor(3, iim_channels, 5)
        iim_total_channels = 3 * (iim_channels * 2)
        self.iim_projector = nn.Conv2d(iim_total_channels, cs, 1)

        # Cross-Attention Fusion Module
        self.fusion = CrossModalAttention(channels=cs, num_heads=8)

    def forward(self, rgb_img, depth_img):
        # 1. RGB Pre-processing
        structure, noise = self.image_denoiser(rgb_img)
        diffuse, specular = self.reflection_separator(structure)

        # 2. Feature Extraction for each branch
        # RGB Branch
        rgb_feat = self.rgb_encoder(structure)

        # IIM Branch
        iim_feat_k3 = self.iim_extractor_k3(diffuse)
        iim_feat_k5 = self.iim_extractor_k5(diffuse)
        iim_feat_raw = torch.cat([iim_feat_k3, iim_feat_k5], dim=1)
        iim_feat = self.iim_projector(iim_feat_raw)

        # Geometric/Depth Branch
        geo_input = torch.cat([depth_img, structure], dim=1)
        geo_fused = self.geo_fusion_conv(geo_input)
        depth_feat = self.depth_encoder(geo_fused)

        # 3. Cross-Modal Fusion
        fused_features = self.fusion(rgb_feat, iim_feat, depth_feat)

        # 4. Prepare auxiliary outputs for potential loss calculation
        aux_data = {
            'decompositions': {'structure': structure, 'noise': noise, 'diffuse': diffuse, 'specular': specular},
            'raw_features': {'rgb': rgb_feat, 'iim': iim_feat, 'depth': depth_feat}
        }
        
        return fused_features, aux_data

# ===============================================================
# 3. Main Network: GraspNet
# ===============================================================

class GraspNet(GraspModel):
    """Multi-modal grasp detection network with attention fusion"""
    def __init__(self, input_channels=4, output_channels=1, channel_size=32, dropout=False, prob=0.0, bilinear=True):
        super().__init__()
        cs = channel_size
        self.dropout = dropout
        self.prob = prob
        
        # Encoder and Fusion Frontend
        self.encoder = FeatureEncoder(channel_size=cs)
        
        # U-Net Backbone (4 layers)
        fusion_out_channels = cs * 3 # Output from CrossModalAttention
        self.down1 = DownSample(fusion_out_channels, cs * 4)
        self.down2 = DownSample(cs * 4, cs * 8)
        self.down3 = DownSample(cs * 8, cs * 16)
        self.down4 = DownSample(cs * 16, cs * 32) # New downsampling layer
        
        self.up1 = UpSample(cs * 32 + cs * 16, cs * 16, bilinear) # New upsampling layer
        self.up2 = UpSample(cs * 16 + cs * 8, cs * 8, bilinear)
        self.up3 = UpSample(cs * 8 + cs * 4, cs * 4, bilinear)
        self.up4 = UpSample(cs * 4 + fusion_out_channels, cs * 2, bilinear)

        # Prediction Heads
        self.final_conv = ConvBlock(cs * 2, cs)
        self.pos_output = nn.Sequential(nn.Conv2d(cs, output_channels, 1), nn.Sigmoid())
        self.cos_output = nn.Sequential(nn.Conv2d(cs, output_channels, 1), nn.Tanh())
        self.sin_output = nn.Sequential(nn.Conv2d(cs, output_channels, 1), nn.Tanh())
        self.width_output = nn.Sequential(nn.Conv2d(cs, output_channels, 1), nn.ReLU())

        # Dropout layers
        self.dropout_pos = nn.Dropout(p=prob)
        self.dropout_cos = nn.Dropout(p=prob)
        self.dropout_sin = nn.Dropout(p=prob)
        self.dropout_wid = nn.Dropout(p=prob)

        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')

    def forward(self, x_in):
        # Input format is assumed to be [B, 4, H, W] with channels [D, R, G, B]
        depth_img, rgb_img = x_in.split([1, 3], dim=1)

        # 1. Get fused features from the multi-modal encoder
        fused_l1, aux_data = self.encoder(rgb_img, depth_img)

        # 2. Pass through U-Net backbone
        x_l2 = self.down1(fused_l1)
        x_l3 = self.down2(x_l2)
        x_l4 = self.down3(x_l3)
        x_l5 = self.down4(x_l4) # Pass through the new layer
        
        x = self.up1(x_l5, x_l4) # New upsampling step
        x = self.up2(x, x_l3)
        x = self.up3(x, x_l2)
        x = self.up4(x, fused_l1)
        
        final_feat = self.final_conv(x)
        
        # 3. Get predictions
        pos = self.pos_output(final_feat)
        cos = self.cos_output(final_feat)
        sin = self.sin_output(final_feat)
        width = self.width_output(final_feat)

        # Apply dropout if enabled
        if self.dropout:
            pos = self.dropout_pos(pos)
            cos = self.dropout_cos(cos)
            sin = self.dropout_sin(sin)
            width = self.dropout_wid(width)

        # Normalize angle outputs
        angle_norm = torch.sqrt(cos**2 + sin**2).clamp(min=1e-8)  # 增加稳定性

        # Return in the format expected by GraspModel.compute_loss
        return pos, cos / angle_norm, sin / angle_norm, width

# ===============================================================
# 4. Usage Example
# ===============================================================
if __name__ == '__main__':
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"--- Demonstrating GraspNet on {device} ---")

    # Instantiate the main network
    model = GraspNet(input_channels=4, channel_size=32).to(device)

    # Check model parameter count
    num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total trainable parameters: {num_params / 1e6:.2f}M")

    # Use smaller input size to avoid memory issues with the attention mechanism
    dummy_input = torch.randn(1, 4, 64, 64).to(device)
    pos_pred, cos_pred, sin_pred, width_pred = model(dummy_input)

    print("\nGraspNet forward pass successful.")
    print(f"  - Predicted 'pos' map shape: {pos_pred.shape}")
    print(f"  - Predicted 'cos' map shape: {cos_pred.shape}")
    print(f"  - Predicted 'sin' map shape: {sin_pred.shape}")
    print(f"  - Predicted 'width' map shape: {width_pred.shape}")

