import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# ===============================================================
# 0. 核心与基础模块 (Core & Base Modules)
# ===============================================================

class ReflectedConvolution(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3):
        super(ReflectedConvolution, self).__init__()
        self.kernel_size = kernel_size
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.rg_bn = nn.BatchNorm2d(out_channels)
        self.gb_bn = nn.BatchNorm2d(out_channels)
        self.rb_bn = nn.BatchNorm2d(out_channels)
        self.filter = nn.Parameter(torch.randn(self.out_channels, self.in_channels, self.kernel_size, self.kernel_size))
        self.init_weights()

    def init_weights(self):
        torch.nn.init.kaiming_normal_(self.filter, mode='fan_out', nonlinearity='relu')
        
    def mean_constraint(self, kernel):
        mean = torch.mean(kernel, dim=[1, 2, 3], keepdim=True)
        return kernel - mean

    def forward(self, img):
        log_img = torch.log(torch.clamp(img, min=1e-6))
        red_chan, green_chan, blue_chan = log_img[:, 0:1], log_img[:, 1:2], log_img[:, 2:3]
        normalized_filter = self.mean_constraint(self.filter)
        filt_r1 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_g1 = F.conv2d(green_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rg = self.rg_bn(filt_r1 + filt_g1)
        filt_g2 = F.conv2d(green_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b1 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_gb = self.gb_bn(filt_g2 + filt_b1)
        filt_r2 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b2 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rb = self.rb_bn(filt_r2 + filt_b2)
        return torch.cat([filt_rg, filt_gb, filt_rb], dim=1)

class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels: mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 3, padding=1), nn.BatchNorm2d(mid_channels), nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, 3, padding=1), nn.BatchNorm2d(out_channels), nn.ReLU(inplace=True))
    def forward(self, x): return self.double_conv(x)

class Down(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(nn.MaxPool2d(2), DoubleConv(in_channels, out_channels))
    def forward(self, x): return self.maxpool_conv(x)

class Up(nn.Module):
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels)
        else:
            self.up = nn.ConvTranspose2d(in_channels // 2, in_channels // 2, 2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)
    def forward(self, x1, x2):
        x1 = self.up(x1)
        diffY, diffX = x2.size()[2] - x1.size()[2], x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

# ===============================================================
# 1. 注意力与预处理模块 (Attention & Pre-processing Modules)
# ===============================================================

class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels, bias=False),
            nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class LightweightNoiseDecoupling(nn.Module):
    def __init__(self, input_channels=3, feat_channels=24):
        super().__init__()
        self.enc1 = DoubleConv(input_channels, feat_channels)
        self.enc2 = Down(feat_channels, feat_channels * 2)
        self.dec1 = Up(feat_channels * 2 + feat_channels, feat_channels, bilinear=True)
        self.out_structure = nn.Conv2d(feat_channels, input_channels, 1)
        self.out_noise = nn.Conv2d(feat_channels, input_channels, 1)
    def forward(self, x):
        x1 = self.enc1(x)
        x2 = self.enc2(x1)
        x_dec = self.dec1(x2, x1)
        structure_layer = torch.tanh(self.out_structure(x_dec))
        noise_layer = self.out_noise(x_dec)
        return structure_layer, noise_layer

class LightweightReflectionSeparation(nn.Module):
    def __init__(self, input_channels=3, feat_channels=24):
        super().__init__()
        self.conv_block = nn.Sequential(DoubleConv(input_channels, feat_channels), DoubleConv(feat_channels, feat_channels))
        self.out_diffuse = nn.Conv2d(feat_channels, input_channels, 1)
        self.out_specular = nn.Conv2d(feat_channels, input_channels, 1)
    def forward(self, x):
        features = self.conv_block(x)
        diffuse_layer = torch.tanh(self.out_diffuse(features))
        specular_layer = F.relu(self.out_specular(features))
        return diffuse_layer, specular_layer

class IIMGrasp(nn.Module):
    def __init__(self, input_channels=3, iim_channels=8):
        super().__init__()
        self.iim_k3 = ReflectedConvolution(1, iim_channels, kernel_size=3)
        self.iim_k5 = ReflectedConvolution(1, iim_channels, kernel_size=5)
        total_iim_channels = 3 * (iim_channels + iim_channels)
        self.fusion_conv = nn.Conv2d(total_iim_channels, total_iim_channels, 1)
    def forward(self, diffuse_layer):
        feat_k3, feat_k5 = self.iim_k3(diffuse_layer), self.iim_k5(diffuse_layer)
        multi_scale_feat = torch.cat([feat_k3, feat_k5], dim=1)
        return self.fusion_conv(multi_scale_feat)

# ===============================================================
# 2. 兼容框架的 GraspModel 基类
# ===============================================================

class GraspModel(nn.Module):
    def __init__(self):
        super(GraspModel, self).__init__()
        self.current_epoch = 0

    def forward(self, x_in):
        raise NotImplementedError()

    def compute_loss(self, xc, yc):
        y_pos, y_cos, y_sin, y_width = yc
        pred_dict = self.forward(xc)
        
        predictions = pred_dict['grasp_predictions']
        pos_pred, cos_pred, sin_pred, width_pred = predictions.values()
        
        pos_loss = F.smooth_l1_loss(pos_pred, y_pos)
        cos_loss = F.smooth_l1_loss(cos_pred, y_cos)
        sin_loss = F.smooth_l1_loss(sin_pred, y_sin)
        width_loss = F.smooth_l1_loss(width_pred, y_width)
        
        angle_consistency_loss = F.mse_loss(cos_pred**2 + sin_pred**2, torch.ones_like(cos_pred))
        
        aux_weight = max(0.1, 1.0 - self.current_epoch / 100.0) 
        
        total_grasp_loss = 2.0 * pos_loss + 1.0 * cos_loss + 1.0 * sin_loss + 1.5 * width_loss
        total_consistency_loss = angle_consistency_loss * 0.1
        
        weighted_aux_losses = {k: v * aux_weight for k, v in pred_dict.get('aux_losses', {}).items()}
        total_aux_loss = sum(weighted_aux_losses.values())
        
        total_loss = total_grasp_loss + total_consistency_loss + total_aux_loss

        return {
            'loss': total_loss,
            'losses': {
                'p_loss': pos_loss, 'cos_loss': cos_loss, 'sin_loss': sin_loss, 'width_loss': width_loss,
                'consistency_loss': total_consistency_loss,
                **{f'aux_{k}': v for k, v in weighted_aux_losses.items()}
            },
            'pred': predictions
        }

    def predict(self, xc):
        pred_dict = self.forward(xc)
        return pred_dict['grasp_predictions']

# ===============================================================
# 3. YOLAGraspUNetV4: 增强的几何分支RGB-D模型
# ===============================================================

class YOLAGraspUNetV4(GraspModel):
    def __init__(self, input_channels=4, output_channels=1, channel_size=32, dropout=False, prob=0.0, bilinear=True, **kwargs):
        super().__init__()
        self.dropout = dropout
        self.prob = prob
        self.use_rgb = (input_channels == 3 or input_channels == 4)
        self.use_depth = (input_channels == 1 or input_channels == 4)

        if not self.use_rgb and not self.use_depth:
            raise ValueError("At least one of RGB or Depth must be used.")

        # --- 1. 预处理前端 ---
        if self.use_rgb:
            self.noise_decoupler = LightweightNoiseDecoupling(3, feat_channels=24)
            self.reflection_separator = LightweightReflectionSeparation(3, feat_channels=24)
        
        # --- 2. 三分支编码器 ---
        cs = channel_size
        
        if self.use_rgb:
            self.rgb_enc1 = DoubleConv(3, cs)
            self.iim_grasp_module = IIMGrasp(3, iim_channels=cs//4) 
            iim_out_channels = 3 * (cs//4 + cs//4)
            self.iim_enc1_proj = nn.Conv2d(iim_out_channels, cs, 1)

        if self.use_depth:
            # 关键改进：几何分支现在接收4通道输入 (Depth + Structure)
            self.depth_enc1 = DoubleConv(1 + 3, cs) if self.use_rgb else DoubleConv(1, cs)
        
        # --- 3. 注意力融合模块 ---
        fusion_channels = 0
        if self.use_rgb: fusion_channels += 2 * cs # RGB and IIM branches
        if self.use_depth: fusion_channels += cs
        self.fusion_attention = ChannelAttention(fusion_channels)
        
        # --- 4. 共享的下采样路径和解码器 ---
        self.down1 = Down(fusion_channels, cs * 2)
        self.down2 = Down(cs * 2, cs * 4)
        self.down3 = Down(cs * 4, cs * 8)
        factor = 2 if bilinear else 1
        self.down4 = Down(cs * 8, cs * 16 // factor)

        self.up1 = Up((cs * 16 // factor) + cs * 8, cs * 8 // factor, bilinear)
        self.up2 = Up((cs * 8 // factor) + cs * 4, cs * 4 // factor, bilinear)  
        self.up3 = Up((cs * 4 // factor) + cs * 2, cs * 2 // factor, bilinear)
        self.up4 = Up((cs * 2 // factor) + fusion_channels, fusion_channels, bilinear)
        
        # --- 5. 最终预测头 ---
        self.final_conv = DoubleConv(fusion_channels, cs)
        self.pos_output = nn.Conv2d(cs, output_channels, 1)
        self.cos_output = nn.Conv2d(cs, output_channels, 1)
        self.sin_output = nn.Conv2d(cs, output_channels, 1)
        self.width_output = nn.Conv2d(cs, output_channels, 1)

        self.dropout_pos = nn.Dropout(p=prob)
        self.dropout_cos = nn.Dropout(p=prob)
        self.dropout_sin = nn.Dropout(p=prob)
        self.dropout_wid = nn.Dropout(p=prob)

        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.xavier_uniform_(m.weight, gain=1)

    def forward(self, x_in):
        # 分离RGB和Depth输入
        # 注意：数据加载器的拼接顺序是 [Depth, R, G, B]
        rgb_in, depth_in = None, None
        if self.use_rgb and self.use_depth:
            depth_in = x_in[:, 0:1, :, :]  # 第0个通道是深度
            rgb_in = x_in[:, 1:4, :, :]    # 第1-3个通道是RGB
        elif self.use_rgb:
            rgb_in = x_in
        elif self.use_depth:
            depth_in = x_in

        features_l1 = []
        aux_losses = {}
        decompositions = {}
        structure_layer = None

        if self.use_rgb:
            structure_layer, noise_layer = self.noise_decoupler(rgb_in)
            diffuse_layer, specular_layer = self.reflection_separator(structure_layer)
            
            rgb_feat_l1 = self.rgb_enc1(structure_layer)
            iim_feat_l1 = self.iim_enc1_proj(self.iim_grasp_module(diffuse_layer))
            features_l1.extend([rgb_feat_l1, iim_feat_l1])

            aux_losses['noise_recon'] = F.l1_loss(structure_layer + noise_layer, rgb_in)
            aux_losses['reflection_recon'] = F.l1_loss(diffuse_layer + specular_layer, structure_layer)
            aux_losses['specular_sparsity'] = torch.mean(torch.abs(specular_layer))
            decompositions = {'structure': structure_layer, 'noise': noise_layer, 'diffuse': diffuse_layer, 'specular': specular_layer}

        if self.use_depth:
            # 关键改进：构建增强的几何分支输入
            if self.use_rgb and structure_layer is not None:
                # 如果有RGB，则拼接Depth和Structure Layer
                geometric_input = torch.cat([depth_in, structure_layer], dim=1)
            else:
                # 如果只有Depth，则只使用Depth
                geometric_input = depth_in
            
            depth_feat_l1 = self.depth_enc1(geometric_input)
            features_l1.append(depth_feat_l1)
        
        fused_feat_l1 = self.fusion_attention(torch.cat(features_l1, dim=1))
        
        x_l2 = self.down1(fused_feat_l1)
        x_l3 = self.down2(x_l2)
        x_l4 = self.down3(x_l3)
        x_l5 = self.down4(x_l4)
        
        x = self.up1(x_l5, x_l4)
        x = self.up2(x, x_l3)
        x = self.up3(x, x_l2)
        x = self.up4(x, fused_feat_l1)
        
        final_feat = self.final_conv(x)

        if self.dropout:
            pos_output = self.pos_output(self.dropout_pos(final_feat))
            cos_output = self.cos_output(self.dropout_cos(final_feat))
            sin_output = self.sin_output(self.dropout_sin(final_feat))
            width_output = self.width_output(self.dropout_wid(final_feat))
        else:
            pos_output, cos_output, sin_output, width_output = self.pos_output(final_feat), self.cos_output(final_feat), self.sin_output(final_feat), self.width_output(final_feat)

        return {
            'grasp_predictions': {'pos': pos_output, 'cos': cos_output, 'sin': sin_output, 'width': width_output},
            'decompositions': decompositions,
            'aux_losses': aux_losses
        }

# ===============================================================
# 4. 使用示例
# ===============================================================
if __name__ == '__main__':
    print("--- 演示RGB-D模型 (4通道输入) ---")
    model_rgbd = YOLAGraspUNetV4(input_channels=4, channel_size=32)
    dummy_input_rgbd = torch.randn(2, 4, 224, 224)
    output_dict_rgbd = model_rgbd(dummy_input_rgbd)
    print("RGB-D模型前向传播成功。")
    print("预测Pos图尺寸:", output_dict_rgbd['grasp_predictions']['pos'].shape)
