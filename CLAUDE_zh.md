# CLAUDE.md

这个文件为Claude Code (claude.ai/code) 提供在代码库中工作的指导。

## 项目概述
这是一个基于生成式残差卷积神经网络（GR-ConvNet）的机器人抓取系统，能够从RGB-D图像中检测物体并预测合适的对极抓取配置。系统支持Cornell和Jacquard数据集，包括训练、评估和实时推理功能。

## 系统架构
- **核心模型**: GR-ConvNet（生成式残差卷积神经网络），包含多个变体（grconvnet, grconvnet2, grconvnet3, grconvnet4）
- **输入数据**: RGB-D图像（默认224x224分辨率）
- **输出结果**: 抓取质量图、角度图、宽度图，用于检测最佳抓取位置
- **设备支持**: CUDA GPU，支持CPU回退

## 核心组件

### 模型模块
- `inference/models/` - GR-ConvNet模型定义
- `utils/data/` - Cornell和Jacquard数据集加载器
- `utils/dataset_processing/` - 图像处理和抓取评估工具
- `hardware/` - 摄像头接口（RealSense）和设备管理

### 训练管道
- `train_network.py` - 主要训练脚本，包含验证和检查点
- `evaluate.py` - 使用IoU指标评估模型
- `logs/` - TensorBoard日志和模型检查点保存目录

### 推理模式
- `run_offline.py` - 单个图像的离线推理
- `run_realtime.py` - 实时摄像头推理，带可视化
- `run_grasp_generator.py` - 生产级抓取生成器，支持ROS集成
- `run_calibration.py` - 摄像头校准工具

## 常用命令

### 安装配置
```bash
# 安装依赖
pip install -r requirements.txt

# 下载数据集
bash utils/get_cornell.sh
bash utils/get_jacquard.sh

# 为Cornell数据集生成深度图像
python -m utils.dataset_processing.generate_cornell_depth <数据集路径>
```

### 模型训练
```bash
# Cornell数据集训练
python train_network.py --dataset cornell --dataset-path <路径> --description training_cornell

# Jacquard数据集训练
python train_network.py --dataset jacquard --dataset-path <路径> --description training_jacquard --use-dropout 0 --input-size 300

# 关键训练参数：
# --network: 模型类型（推荐使用grconvnet3）
# --input-size: 图像大小（Cornell用224，Jacquard用300）
# --use-depth/--use-rgb: 启用深度/RGB输入通道
# --channel-size: 网络宽度（16/32）
```

### 模型评估
```bash
# 评估训练好的模型
python evaluate.py --network <模型路径> --dataset cornell --dataset-path <路径> --iou-eval

# 单个图像离线推理
python run_offline.py --network <模型路径> --rgb_path <图像.png> --depth_path <深度.tiff>
```

### 实时使用
```bash
# 实时摄像头推理
python run_realtime.py --network <模型路径>

# 生产级抓取生成器（支持ROS）
python run_grasp_generator.py
```

### 模型管理
```bash
# 清理小型训练日志
./cleanup.sh

# 训练好的模型位置
trained-models/
├── cornell-randsplit-rgbd-grconvnet3-drop1-ch16/
├── cornell-randsplit-rgbd-grconvnet3-drop1-ch32/
└── jacquard-d-grconvnet3-drop0-ch32/
```

## 重要文件和目录
- `train_network.py:208-345` - 主训练循环和验证
- `inference/models/grconvnet3.py:51-75` - 核心GR-ConvNet前向传播
- `utils/data/cornell_data.py:42-70` - Cornell数据集处理
- `hardware/camera.py` - RealSense摄像头接口
- `inference/grasp_generator.py:50-106` - 生产级抓取生成循环

## 测试和验证
- IoU阈值：0.25（默认）用于抓取评估
- 每10个周期和最佳IoU时保存模型检查点
- TensorBoard日志保存在`logs/`目录
- 预训练模型在`trained-models/`目录可用

## 硬件要求
- 推荐使用支持CUDA的NVIDIA GPU
- 实时推理需要Intel RealSense摄像头（D435/D415）
- Python 3.6+ 和 PyTorch 1.12.1