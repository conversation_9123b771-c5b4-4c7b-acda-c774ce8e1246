import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# ===============================================================
# 0. 核心与基础模块 (Core & Base Modules)
# ===============================================================

class ReflectedConvolution(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3):
        super(ReflectedConvolution, self).__init__()
        self.kernel_size = kernel_size
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.rg_bn = nn.BatchNorm2d(out_channels)
        self.gb_bn = nn.BatchNorm2d(out_channels)
        self.rb_bn = nn.BatchNorm2d(out_channels)
        self.filter = nn.Parameter(torch.randn(self.out_channels, self.in_channels, self.kernel_size, self.kernel_size))
        self.init_weights()

    def init_weights(self):
        torch.nn.init.kaiming_normal_(self.filter, mode='fan_out', nonlinearity='relu')
        
    def mean_constraint(self, kernel):
        mean = torch.mean(kernel, dim=[1, 2, 3], keepdim=True)
        return kernel - mean

    def forward(self, img):
        log_img = torch.log(torch.clamp(img, min=1e-6))
        red_chan, green_chan, blue_chan = log_img[:, 0:1], log_img[:, 1:2], log_img[:, 2:3]
        normalized_filter = self.mean_constraint(self.filter)
        filt_r1 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_g1 = F.conv2d(green_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rg = self.rg_bn(filt_r1 + filt_g1)
        filt_g2 = F.conv2d(green_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b1 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_gb = self.gb_bn(filt_g2 + filt_b1)
        filt_r2 = F.conv2d(red_chan, weight=normalized_filter, padding=self.kernel_size//2)
        filt_b2 = F.conv2d(blue_chan, weight=-normalized_filter, padding=self.kernel_size//2)
        filt_rb = self.rb_bn(filt_r2 + filt_b2)
        return torch.cat([filt_rg, filt_gb, filt_rb], dim=1)

class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels: mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 3, padding=1), nn.BatchNorm2d(mid_channels), nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, 3, padding=1), nn.BatchNorm2d(out_channels), nn.ReLU(inplace=True))
    def forward(self, x): return self.double_conv(x)

class Down(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(nn.MaxPool2d(2), DoubleConv(in_channels, out_channels))
    def forward(self, x): return self.maxpool_conv(x)

class Up(nn.Module):
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            # 在bilinear模式下，输入通道数就是拼接后的总通道数
            # x1上采样后保持in_channels，与x2拼接后总通道数需要在调用时确定
            self.conv = DoubleConv(in_channels, out_channels)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, 2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)
    def forward(self, x1, x2):
        x1 = self.up(x1)
        diffY, diffX = x2.size()[2] - x1.size()[2], x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

# ===============================================================
# 1. 注意力与预处理模块 (Attention & Pre-processing Modules)
# ===============================================================

class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels, bias=False),
            nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class LightweightNoiseDecoupling(nn.Module):
    def __init__(self, input_channels=3, feat_channels=24):
        super().__init__()
        self.enc1 = DoubleConv(input_channels, feat_channels)
        self.enc2 = Down(feat_channels, feat_channels * 2)
        # 拼接后的通道数: feat_channels * 2 + feat_channels = feat_channels * 3
        self.dec1 = Up(feat_channels * 3, feat_channels, bilinear=True)
        self.out_structure = nn.Conv2d(feat_channels, input_channels, 1)
        self.out_noise = nn.Conv2d(feat_channels, input_channels, 1)
    def forward(self, x):
        x1 = self.enc1(x)
        x2 = self.enc2(x1)
        x_dec = self.dec1(x2, x1)
        structure_layer = torch.tanh(self.out_structure(x_dec))
        noise_layer = self.out_noise(x_dec)
        return structure_layer, noise_layer

class LightweightReflectionSeparation(nn.Module):
    def __init__(self, input_channels=3, feat_channels=24):
        super().__init__()
        self.conv_block = nn.Sequential(DoubleConv(input_channels, feat_channels), DoubleConv(feat_channels, feat_channels))
        self.out_diffuse = nn.Conv2d(feat_channels, input_channels, 1)
        self.out_specular = nn.Conv2d(feat_channels, input_channels, 1)
    def forward(self, x):
        features = self.conv_block(x)
        diffuse_layer = torch.tanh(self.out_diffuse(features))
        specular_layer = F.relu(self.out_specular(features))
        return diffuse_layer, specular_layer

class IIMGrasp(nn.Module):
    def __init__(self, input_channels=3, iim_channels=8):
        super().__init__()
        self.iim_k3 = ReflectedConvolution(1, iim_channels, kernel_size=3)
        self.iim_k5 = ReflectedConvolution(1, iim_channels, kernel_size=5)
        total_iim_channels = 3 * (iim_channels + iim_channels)
        self.fusion_conv = nn.Conv2d(total_iim_channels, total_iim_channels, 1)
    def forward(self, diffuse_layer):
        feat_k3, feat_k5 = self.iim_k3(diffuse_layer), self.iim_k5(diffuse_layer)
        multi_scale_feat = torch.cat([feat_k3, feat_k5], dim=1)
        return self.fusion_conv(multi_scale_feat)

# ===============================================================
# 2. 兼容框架的 GraspModel 基类
# ===============================================================

class GraspModel(nn.Module):
    """
    与项目框架兼容的GraspModel基类。
    """
    def __init__(self):
        super(GraspModel, self).__init__()
        self.current_epoch = 0

    def forward(self, x_in):
        raise NotImplementedError()

    def compute_loss(self, xc, yc):
        """
        计算损失，并返回与训练脚本兼容的字典格式。
        """
        y_pos, y_cos, y_sin, y_width = yc
        pred_dict = self.forward(xc)
        
        # 从预测字典中获取抓取预测结果
        predictions = pred_dict['grasp_predictions']
        pos_pred, cos_pred, sin_pred, width_pred = predictions.values()
        
        # 1. 任务特定权重的抓取损失
        pos_loss = F.smooth_l1_loss(pos_pred, y_pos)
        cos_loss = F.smooth_l1_loss(cos_pred, y_cos)
        sin_loss = F.smooth_l1_loss(sin_pred, y_sin)
        width_loss = F.smooth_l1_loss(width_pred, y_width)
        
        # 2. 角度一致性约束损失
        angle_consistency_loss = F.mse_loss(cos_pred**2 + sin_pred**2, torch.ones_like(cos_pred))
        
        # 3. 辅助损失权重衰减
        aux_weight = max(0.1, 1.0 - self.current_epoch / 100.0) 
        
        # 计算加权后的主损失和辅助损失
        total_grasp_loss = 2.0 * pos_loss + 1.0 * cos_loss + 1.0 * sin_loss + 1.5 * width_loss
        total_consistency_loss = angle_consistency_loss * 0.1
        
        weighted_aux_losses = {k: v * aux_weight for k, v in pred_dict.get('aux_losses', {}).items()}
        total_aux_loss = sum(weighted_aux_losses.values())
        
        # 最终总损失
        total_loss = total_grasp_loss + total_consistency_loss + total_aux_loss

        # 组织成训练脚本期望的格式
        return {
            'loss': total_loss,
            'losses': {
                'p_loss': pos_loss,
                'cos_loss': cos_loss,
                'sin_loss': sin_loss,
                'width_loss': width_loss,
                'consistency_loss': total_consistency_loss,
                **{f'aux_{k}': v for k, v in weighted_aux_losses.items()} # 添加带权重的辅助损失
            },
            'pred': predictions
        }

    def predict(self, xc):
        """
        推理阶段的预测。
        """
        pred_dict = self.forward(xc)
        return pred_dict['grasp_predictions']

# ===============================================================
# 3. YOLAGraspUNetV3: 最终模型
# ===============================================================

class YOLAGraspUNetV3(GraspModel):
    def __init__(self, input_channels=4, output_channels=1, channel_size=32, dropout=False, prob=0.0, bilinear=True, **kwargs):
        super().__init__()
        self.dropout = dropout
        self.prob = prob

        # --- 1. 轻量化预处理前端 ---
        self.noise_decoupler = LightweightNoiseDecoupling(input_channels, feat_channels=24)
        self.reflection_separator = LightweightReflectionSeparation(input_channels, feat_channels=24)
        
        # --- 2. 三分支编码器 ---
        cs = channel_size
        
        self.rgb_enc1 = DoubleConv(input_channels, cs)
        self.iim_grasp_module = IIMGrasp(input_channels, iim_channels=cs//4) 
        iim_out_channels = 3 * (cs//4 + cs//4)
        self.iim_enc1_proj = nn.Conv2d(iim_out_channels, cs, 1)
        self.shape_enc1 = DoubleConv(input_channels, cs)

        # --- 3. 注意力融合模块 ---
        self.fusion_attention = ChannelAttention(cs * 3)
        
        # --- 4. 共享的下采样路径和解码器 ---
        self.down1 = Down(cs * 3, cs * 2)
        self.down2 = Down(cs * 2, cs * 4)
        self.down3 = Down(cs * 4, cs * 8)
        factor = 2 if bilinear else 1
        self.down4 = Down(cs * 8, cs * 16 // factor)
        # Up模块：输入通道数 = 上一层输出 + 跳跃连接
        self.up1 = Up((cs * 16 // factor) + cs * 8, cs * 8 // factor, bilinear)
        self.up2 = Up((cs * 8 // factor) + cs * 4, cs * 4 // factor, bilinear)  
        self.up3 = Up((cs * 4 // factor) + cs * 2, cs * 2 // factor, bilinear)
        self.up4 = Up((cs * 2 // factor) + cs * 3, cs * 3, bilinear)
        
        # --- 5. 最终预测头 ---
        self.final_conv = DoubleConv(cs * 3, cs)
        self.pos_output = nn.Conv2d(cs, output_channels, 1)
        self.cos_output = nn.Conv2d(cs, output_channels, 1)
        self.sin_output = nn.Conv2d(cs, output_channels, 1)
        self.width_output = nn.Conv2d(cs, output_channels, 1)

        # --- 6. Dropout层 ---
        self.dropout_pos = nn.Dropout(p=prob)
        self.dropout_cos = nn.Dropout(p=prob)
        self.dropout_sin = nn.Dropout(p=prob)
        self.dropout_wid = nn.Dropout(p=prob)

        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.xavier_uniform_(m.weight, gain=1)

    def forward(self, x_in):
        structure_layer, noise_layer = self.noise_decoupler(x_in)
        diffuse_layer, specular_layer = self.reflection_separator(structure_layer)
        
        rgb_feat_l1 = self.rgb_enc1(structure_layer)
        iim_feat_l1 = self.iim_enc1_proj(self.iim_grasp_module(diffuse_layer))
        shape_feat_l1 = self.shape_enc1(structure_layer)
        
        fused_feat_l1_before_attention = torch.cat([rgb_feat_l1, iim_feat_l1, shape_feat_l1], dim=1)
        fused_feat_l1 = self.fusion_attention(fused_feat_l1_before_attention)
        
        x_l2 = self.down1(fused_feat_l1)
        x_l3 = self.down2(x_l2)
        x_l4 = self.down3(x_l3)
        x_l5 = self.down4(x_l4)
        
        x = self.up1(x_l5, x_l4)
        x = self.up2(x, x_l3)
        x = self.up3(x, x_l2)
        x = self.up4(x, fused_feat_l1)
        
        final_feat = self.final_conv(x)

        if self.dropout:
            pos_output = self.pos_output(self.dropout_pos(final_feat))
            cos_output = self.cos_output(self.dropout_cos(final_feat))
            sin_output = self.sin_output(self.dropout_sin(final_feat))
            width_output = self.width_output(self.dropout_wid(final_feat))
        else:
            pos_output = self.pos_output(final_feat)
            cos_output = self.cos_output(final_feat)
            sin_output = self.sin_output(final_feat)
            width_output = self.width_output(final_feat)

        noise_recon_loss = F.l1_loss(structure_layer + noise_layer, x_in)
        reflection_recon_loss = F.l1_loss(diffuse_layer + specular_layer, structure_layer)
        specular_sparsity_loss = torch.mean(torch.abs(specular_layer))

        return {
            'grasp_predictions': {'pos': pos_output, 'cos': cos_output, 'sin': sin_output, 'width': width_output},
            'decompositions': {'structure': structure_layer, 'noise': noise_layer, 'diffuse': diffuse_layer, 'specular': specular_layer},
            'aux_losses': {'noise_recon': noise_recon_loss, 'reflection_recon': reflection_recon_loss, 'specular_sparsity': specular_sparsity_loss}
        }

# ===============================================================
# 4. 使用示例
# ===============================================================
if __name__ == '__main__':
    # 演示模型创建
    model = YOLAGraspUNetV3(input_channels=3, output_channels=1, channel_size=32, dropout=True, prob=0.1)
    dummy_input = torch.randn(2, 3, 224, 224)
    print("模型创建成功，输入张量尺寸:", dummy_input.shape)
    
    # 演示损失计算
    gt_pos = torch.rand(2, 1, 224, 224)
    gt_cos = torch.rand(2, 1, 224, 224)
    gt_sin = torch.rand(2, 1, 224, 224)
    gt_width = torch.rand(2, 1, 224, 224)
    dummy_gt = (gt_pos, gt_cos, gt_sin, gt_width)
    
    loss_dict = model.compute_loss(dummy_input, dummy_gt)
    
    print(f"\n计算损失成功:")
    print(f"  - Total Loss: {loss_dict['loss'].item():.4f}")
    print("  - Breakdown:")
    for name, loss in loss_dict['losses'].items():
        print(f"    - {name}: {loss.item():.4f}")

    # 演示推理
    pred_dict = model.predict(dummy_input)
    print("\n推理成功:")
    for name, tensor in pred_dict.items():
        print(f"  - {name}: {tensor.shape}")
