#!/usr/bin/env python3
"""
Test script to verify that GraspNet can be instantiated with training parameters
"""

import torch
import sys
import os

# Add the inference/models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'inference', 'models'))

from inference.models import get_network

def test_graspnet_instantiation():
    """Test that GraspNet can be instantiated with all expected parameters"""
    
    print("Testing GraspNet instantiation...")
    
    # Test parameters similar to what train_network2.py uses
    input_channels = 4  # depth + RGB
    use_dropout = True
    dropout_prob = 0.1
    channel_size = 32
    
    try:
        # Get the network class
        network = get_network('graspnet')
        print(f"✓ Successfully imported GraspNet: {network}")
        
        # Instantiate with training parameters
        net = network(
            input_channels=input_channels,
            dropout=use_dropout,
            prob=dropout_prob,
            channel_size=channel_size
        )
        print(f"✓ Successfully instantiated GraspNet with parameters:")
        print(f"  - input_channels: {input_channels}")
        print(f"  - dropout: {use_dropout}")
        print(f"  - prob: {dropout_prob}")
        print(f"  - channel_size: {channel_size}")
        
        # Test forward pass
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        net = net.to(device)
        
        # Use small input to avoid memory issues
        dummy_input = torch.randn(1, 4, 64, 64).to(device)
        
        # Test forward pass
        with torch.no_grad():
            output = net(dummy_input)
            print(f"✓ Forward pass successful")
            print(f"  - Output type: {type(output)}")
            if isinstance(output, tuple) and len(output) == 4:
                pos, cos, sin, width = output
                print(f"  - pos shape: {pos.shape}")
                print(f"  - cos shape: {cos.shape}")
                print(f"  - sin shape: {sin.shape}")
                print(f"  - width shape: {width.shape}")
            else:
                print(f"  - Unexpected output format: {output}")
        
        # Test compute_loss method (inherited from GraspModel)
        if hasattr(net, 'compute_loss'):
            print(f"✓ GraspNet has compute_loss method")
        else:
            print(f"✗ GraspNet missing compute_loss method")
            
        print("\n🎉 All tests passed! GraspNet is ready for training.")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_graspnet_instantiation()
    sys.exit(0 if success else 1)
